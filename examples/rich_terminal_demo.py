#!/usr/bin/env python3
"""
Rich Terminal Interface Demo

Demonstrates the enhanced CLI features including rich formatting,
progress tracking, workflow visualization, and interactive UX.
"""

import time
import sys
import random
from pathlib import Path

# Add the parent directory to the path so we can import the agent framework
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from agent_framework.cli.rich_formatter import RichTerminalFormatter
    from agent_framework.cli.progress_tracker import AdvancedProgressTracker, MultiStepProgressTracker
    from agent_framework.cli.workflow_visualizer import WorkflowVisualizer, WorkflowPhase
    from agent_framework.cli.interactive_ux import InteractiveUX, ActionableError, MessageType
    from agent_framework.cli.utils import CLIUtils
    RICH_AVAILABLE = True
except ImportError as e:
    print(f"Rich terminal features not available: {e}")
    print("Please install the rich library: pip install rich")
    sys.exit(1)


def demo_basic_formatting():
    """Demo basic rich formatting features."""
    formatter = RichTerminalFormatter()
    
    formatter.print_header("Rich Terminal Formatting Demo", "Showcasing enhanced CLI capabilities")
    
    # Basic message types
    formatter.print_section("Message Types")
    formatter.print_success("Operation completed successfully!")
    formatter.print_error("An error occurred during processing")
    formatter.print_warning("This is a warning message")
    formatter.print_info("Here's some useful information")
    formatter.print_debug("Debug information for developers")
    
    # Tables
    formatter.print_section("Data Tables")
    headers = ["Component", "Status", "Version", "Last Updated"]
    rows = [
        ["Rich Formatter", "✅ Active", "1.0.0", "2024-01-15"],
        ["Progress Tracker", "✅ Active", "1.2.1", "2024-01-14"],
        ["Workflow Visualizer", "⚠️ Beta", "0.9.5", "2024-01-13"],
        ["Interactive UX", "✅ Active", "1.1.0", "2024-01-12"]
    ]
    formatter.print_table(headers, rows, "System Components")
    
    # Code blocks
    formatter.print_section("Code Highlighting")
    code_example = '''
def enhanced_cli_demo():
    """Demonstrate enhanced CLI features."""
    formatter = RichTerminalFormatter()
    formatter.print_success("Rich formatting enabled!")
    
    with formatter.status("Processing..."):
        time.sleep(2)
    
    return "Demo completed"
    '''
    formatter.print_code(code_example, "python", "Python Code Example")
    
    # JSON data
    formatter.print_section("JSON Data")
    sample_data = {
        "application": "Agent Framework",
        "version": "2.0.0",
        "features": ["Rich Terminal", "Progress Tracking", "Workflow Visualization"],
        "config": {
            "theme": "dark",
            "animations": True,
            "colors": 256
        }
    }
    formatter.print_json(sample_data, "Configuration Data")
    
    # Panels and layouts
    formatter.print_section("Panels and Layouts")
    formatter.print_panel(
        "This is content inside a panel with a border.\nPanels are great for highlighting important information.",
        "Information Panel",
        "blue"
    )
    
    # Lists
    formatter.print_section("Lists")
    features = [
        "Color-coded output for different message types",
        "Progress bars for long-running operations",
        "Formatted tables for structured data",
        "Syntax highlighting for code snippets",
        "Interactive prompts with validation",
        "Workflow visualization with dependencies"
    ]
    formatter.print_list(features)


def demo_progress_tracking():
    """Demo advanced progress tracking."""
    formatter = RichTerminalFormatter()
    formatter.print_header("Progress Tracking Demo", "Advanced progress indicators and tracking")
    
    # Basic progress tracker
    tracker = AdvancedProgressTracker()
    
    # Create multiple operations
    operations = [
        ("download", "Downloading Files", "Downloading required files", 100),
        ("process", "Processing Data", "Processing downloaded data", 50),
        ("analyze", "Analyzing Results", "Analyzing processed data", None),  # Indeterminate
        ("upload", "Uploading Results", "Uploading analysis results", 25)
    ]
    
    for op_id, name, desc, total in operations:
        tracker.create_operation(op_id, name, desc, total)
    
    # Simulate operations with progress context
    with tracker.progress_context(*[op[0] for op in operations], title="File Processing Pipeline"):
        for op_id, name, desc, total in operations:
            tracker.start_operation(op_id)
            
            if total:  # Determinate progress
                for i in range(0, total + 1, 10):
                    tracker.update_operation(op_id, completed_work=i)
                    time.sleep(0.1)
            else:  # Indeterminate progress
                for i in range(20):
                    tracker.update_operation(op_id, increment=1, message=f"{desc} - Step {i+1}")
                    time.sleep(0.05)
            
            tracker.complete_operation(op_id, f"{name} completed successfully")
    
    # Print summary
    tracker.print_summary("Operation Summary")


def demo_workflow_visualization():
    """Demo workflow visualization."""
    formatter = RichTerminalFormatter()
    formatter.print_header("Workflow Visualization Demo", "Step-by-step workflow with dependencies")
    
    visualizer = WorkflowVisualizer()
    
    # Define a complex workflow with dependencies
    workflow_steps = [
        ("init", "Initialize System", "Set up environment and configuration", WorkflowPhase.INITIALIZATION),
        ("validate", "Validate Input", "Check input parameters and files", WorkflowPhase.PREPARATION, ["init"]),
        ("preprocess", "Preprocess Data", "Clean and prepare data", WorkflowPhase.PREPARATION, ["validate"]),
        ("train", "Train Model", "Execute machine learning training", WorkflowPhase.EXECUTION, ["preprocess"]),
        ("evaluate", "Evaluate Model", "Test model performance", WorkflowPhase.VALIDATION, ["train"]),
        ("optimize", "Optimize Parameters", "Fine-tune model parameters", WorkflowPhase.EXECUTION, ["evaluate"]),
        ("test", "Final Testing", "Run comprehensive tests", WorkflowPhase.VALIDATION, ["optimize"]),
        ("deploy", "Deploy Model", "Deploy to production", WorkflowPhase.FINALIZATION, ["test"]),
        ("cleanup", "Cleanup Resources", "Clean up temporary files", WorkflowPhase.CLEANUP, ["deploy"])
    ]
    
    # Add steps to workflow
    for step_data in workflow_steps:
        step_id, name, desc, phase = step_data[:4]
        deps = step_data[4] if len(step_data) > 4 else []
        visualizer.add_step(step_id, name, desc, phase, deps, total_work=random.randint(10, 100))
    
    # Start workflow
    visualizer.start_workflow("ML Model Training Pipeline")
    
    # Execute workflow respecting dependencies
    completed_steps = set()
    
    while len(completed_steps) < len(workflow_steps):
        available_steps = visualizer.get_next_available_steps()
        
        if not available_steps:
            break
        
        # Execute next available step
        step_id = available_steps[0]
        step = visualizer.steps[step_id]
        
        visualizer.start_step(step_id)
        
        # Simulate work with progress updates
        if step.total_work:
            for i in range(0, step.total_work + 1, 5):
                visualizer.update_step_progress(step_id, completed_work=i)
                time.sleep(0.02)
        
        # Randomly fail some steps to demonstrate error handling
        if random.random() < 0.1:  # 10% chance of failure
            visualizer.fail_step(step_id, "Simulated random failure")
        else:
            visualizer.complete_step(step_id, f"Successfully completed {step.name}")
            completed_steps.add(step_id)
    
    # Print workflow summary
    visualizer.print_workflow_summary()


def demo_interactive_ux():
    """Demo interactive UX features."""
    formatter = RichTerminalFormatter()
    ux = InteractiveUX()
    
    formatter.print_header("Interactive UX Demo", "Enhanced user experience features")
    
    # Welcome banner
    ux.show_welcome_banner(
        "Agent Framework CLI",
        version="2.0.0",
        description="Enhanced terminal interface with rich formatting and interactive features"
    )
    
    # Different message types
    formatter.print_section("Message Types")
    ux.show_success("Configuration loaded successfully")
    ux.show_info("System initialized with default settings")
    ux.show_warning("Some optional features are disabled")
    
    # Actionable error
    formatter.print_section("Actionable Error Reporting")
    error = ActionableError(
        message="Failed to connect to remote server",
        suggestions=[
            "Check your internet connection",
            "Verify the server URL is correct",
            "Ensure firewall allows outbound connections",
            "Try using a different network"
        ],
        error_code="CONN_001",
        documentation_url="https://docs.example.com/troubleshooting/connection-errors"
    )
    ux.show_actionable_error(error)
    
    # Loading spinner demo
    formatter.print_section("Loading Indicators")
    
    def simulate_loading():
        time.sleep(2)
        return "Data loaded successfully"
    
    result = ux.show_loading_spinner("Loading configuration data...", simulate_loading)
    ux.show_success(result)
    
    # Multi-step progress
    formatter.print_section("Multi-Step Progress")
    steps = [
        "Initializing components",
        "Loading configuration",
        "Connecting to services",
        "Validating setup",
        "Starting application"
    ]
    
    def execute_step(index, description):
        time.sleep(0.5)  # Simulate work
        return f"Step {index + 1} completed"
    
    results = ux.show_progress_with_steps(steps, execute_step)
    ux.show_success(f"All {len(results)} steps completed successfully")
    
    # Summary table
    formatter.print_section("Summary Information")
    summary_data = {
        "total_operations": 15,
        "successful_operations": 13,
        "failed_operations": 2,
        "execution_time": "2.5 seconds",
        "memory_usage": "45.2 MB",
        "status": "Completed with warnings"
    }
    ux.show_summary_table("Execution Summary", summary_data)


def demo_cli_utils_integration():
    """Demo CLI utils with rich integration."""
    formatter = RichTerminalFormatter()
    formatter.print_header("CLI Utils Integration Demo", "Enhanced CLI utilities with rich support")
    
    # Test both rich and fallback modes
    utils_rich = CLIUtils(use_rich=True)
    utils_fallback = CLIUtils(use_rich=False)
    
    formatter.print_section("Rich Mode vs Fallback Mode")
    
    formatter.print_info(f"Rich support available: {utils_rich.has_rich_support()}")
    formatter.print_info(f"Fallback mode: {not utils_fallback.has_rich_support()}")
    
    # Demonstrate various utilities
    formatter.print_section("Utility Methods")
    
    # Step indicators
    for i in range(1, 4):
        utils_rich.print_step(i, 3, f"Processing step {i}")
        time.sleep(0.5)
    
    # Separators and formatting
    utils_rich.print_separator("═", "blue")
    utils_rich.print_centered("Centered Text Example")
    utils_rich.print_separator("─", "dim")
    
    # Lists and panels
    items = ["Enhanced formatting", "Progress tracking", "Interactive prompts", "Error handling"]
    utils_rich.print_list(items, "green")
    
    if utils_rich.has_rich_support():
        utils_rich.print_panel(
            "This panel demonstrates the rich formatting capabilities.\nIt supports multi-line content with proper styling.",
            "Rich Panel Example",
            "cyan"
        )


def main():
    """Run all demos."""
    try:
        formatter = RichTerminalFormatter()
        
        # Main demo menu
        formatter.print_header("🚀 Rich Terminal Interface Demo", "Agent Framework Enhanced CLI")
        
        demos = [
            ("Basic Formatting", demo_basic_formatting),
            ("Progress Tracking", demo_progress_tracking),
            ("Workflow Visualization", demo_workflow_visualization),
            ("Interactive UX", demo_interactive_ux),
            ("CLI Utils Integration", demo_cli_utils_integration)
        ]
        
        formatter.print_info("This demo showcases the enhanced terminal interface features.")
        formatter.print_info("Each section demonstrates different capabilities.\n")
        
        for i, (name, demo_func) in enumerate(demos, 1):
            formatter.print_step(i, len(demos), f"Running {name} Demo")
            
            try:
                demo_func()
                formatter.print_success(f"✅ {name} demo completed")
            except Exception as e:
                formatter.print_error(f"❌ {name} demo failed: {str(e)}")
            
            if i < len(demos):
                formatter.print_separator()
                time.sleep(1)  # Brief pause between demos
        
        # Final summary
        formatter.print_header("Demo Complete! 🎉", "All enhanced CLI features demonstrated")
        
        summary_items = [
            "Rich terminal formatting with colors and styles",
            "Advanced progress tracking with real-time updates",
            "Workflow visualization with dependency management",
            "Interactive UX with actionable error reporting",
            "Integrated CLI utilities with fallback support"
        ]
        
        formatter.print_list(summary_items)
        formatter.print_success("The Agent Framework now has a beautiful, functional CLI!")
        
    except KeyboardInterrupt:
        formatter.print_warning("\nDemo interrupted by user")
    except Exception as e:
        formatter.print_error(f"Demo failed with error: {str(e)}")
        raise


if __name__ == "__main__":
    main()
