--- a/user_service.py
+++ b/user_service.py
@@ -1,8 +1,11 @@
 # user_service.py
 def get_user(user_id):
     """Fetches a user from the database."""
-    if not user_id:
+    if not isinstance(user_id, int) or user_id <= 0:
+        print("Error: Invalid user_id provided.")
         return None
     # DB logic here
     print(f"Fetching user {user_id}")
     return {"id": user_id, "name": "<PERSON>"}
 def delete_user(user_id):
-    # TODO: Implement user deletion
-    pass
+    """Deletes a user."""
+    print(f"Deleting user {user_id}")
+    return True
