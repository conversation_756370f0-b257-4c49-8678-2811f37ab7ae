#!/usr/bin/env python3
"""
Test script for the enhanced TaskExecutor implementation.
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/agent-test')

try:
    from agent_framework.execution.executor import TaskExecutor, TaskExecutionMetrics, TaskValidationResult
    from agent_framework.core.config import FrameworkConfig
    from agent_framework.core.types import Task, TaskPriority
    
    print("✓ All imports successful")
    
    async def test_enhanced_executor():
        """Test the enhanced TaskExecutor functionality."""
        print("\n=== Testing Enhanced TaskExecutor ===")
        
        # Initialize executor
        config = FrameworkConfig()
        executor = TaskExecutor(config)
        print("✓ TaskExecutor initialized")
        
        # Test metrics
        metrics = executor.get_metrics()
        print(f"✓ Initial metrics: {metrics.total_tasks} total, {metrics.completed_tasks} completed")
        assert isinstance(metrics, TaskExecutionMetrics)
        
        # Test task validation
        task = Task(
            name='test_validation_task',
            task_type='test',
            priority=TaskPriority.NORMAL,
            parameters={'test_param': 'test_value'},
            timeout_seconds=30.0
        )
        
        # Test basic validation
        validation_result = executor._validate_task_basic(task)
        print(f"✓ Basic validation: {validation_result.is_valid}")
        assert validation_result.is_valid
        
        # Test parameter validation
        param_validation = executor._validate_task_parameters(task)
        print(f"✓ Parameter validation: {param_validation.is_valid}")
        assert param_validation.is_valid
        
        # Test dependency validation
        dep_validation = executor._validate_task_dependencies(task)
        print(f"✓ Dependency validation: {dep_validation.is_valid}")
        assert dep_validation.is_valid
        
        # Test timeout validation
        timeout_validation = executor._validate_task_timeout(task)
        print(f"✓ Timeout validation: {timeout_validation.is_valid}")
        assert timeout_validation.is_valid
        
        # Test system status
        status = executor.get_system_status()
        print(f"✓ System status: {status['overall_health']}")
        assert 'overall_health' in status
        assert 'components' in status
        assert 'performance' in status
        
        # Test health check
        health = executor.health_check()
        print(f"✓ Health check: {health['status']}")
        assert 'status' in health
        assert 'checks' in health
        
        # Test invalid task validation
        invalid_task = Task(
            name='',  # Invalid empty name
            task_type='test',
            priority=TaskPriority.NORMAL,
            parameters={}
        )
        
        invalid_validation = executor._validate_task_basic(invalid_task)
        print(f"✓ Invalid task validation: {not invalid_validation.is_valid}")
        assert not invalid_validation.is_valid
        assert len(invalid_validation.errors) > 0
        
        print("\n=== All Enhanced TaskExecutor Tests Passed! ===")
        
        return True
        
    # Run the test
    if __name__ == "__main__":
        result = asyncio.run(test_enhanced_executor())
        if result:
            print("\n🎉 Enhanced TaskExecutor implementation is working correctly!")
            sys.exit(0)
        else:
            print("\n❌ Tests failed")
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
