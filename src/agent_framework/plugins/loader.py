"""
Plugin loader for dynamic loading and instantiation of plugins.
"""

import importlib
import importlib.util
import inspect
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional  # removed unused: Type

from ..core.types import PluginInterface
from .registry import PluginMetadata, PluginRegistry


class PluginLoader:
    """
    Dynamic plugin loader for loading and instantiating plugins.

    Supports loading plugins from Python files, modules, and packages
    with proper error handling and security considerations.
    """

    def __init__(self, registry: PluginRegistry, allowed_imports: Optional[List[str]] = None):
        """Initialize the plugin loader."""
        self.logger = logging.getLogger(__name__)
        self.registry = registry
        self.allowed_imports: List[str] = allowed_imports or []
        self._loaded_modules: Dict[str, Any] = {}
        self._plugin_instances: Dict[str, PluginInterface] = {}

    async def discover_plugins(self, plugin_directories: List[str]) -> List[PluginMetadata]:
        """Discover plugins in the specified directories."""
        discovered: List[PluginMetadata] = []

        for directory in plugin_directories:
            path = Path(directory)
            if not path.exists():
                self.logger.warning(f"Plugin directory does not exist: {directory}")
                continue

            self.logger.info(f"Discovering plugins in: {directory}")

            # Look for Python files
            for file_path in path.rglob("*.py"):
                if file_path.name.startswith("_"):
                    continue  # Skip private files

                try:
                    metadata = await self._extract_plugin_metadata(file_path)
                    if metadata:
                        discovered.append(metadata)
                        self.logger.info(f"Discovered plugin: {metadata.name}")

                except Exception as e:
                    self.logger.error(f"Error discovering plugin in {file_path}: {e}")

        return discovered

    async def _extract_plugin_metadata(self, file_path: Path) -> Optional[PluginMetadata]:
        """Extract plugin metadata from a Python file."""
        try:
            # Load the module
            spec = importlib.util.spec_from_file_location(file_path.stem, file_path)
            if not spec or not spec.loader:
                return None

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # Look for plugin classes
            plugin_classes = []
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, PluginInterface) and
                    obj is not PluginInterface and
                    obj.__module__ == module.__name__):
                    plugin_classes.append((name, obj))

            if not plugin_classes:
                return None

            # Use the first plugin class found
            class_name, plugin_class = plugin_classes[0]

            # Extract metadata from class attributes or docstring
            name = getattr(plugin_class, 'PLUGIN_NAME', class_name)
            version = getattr(plugin_class, 'PLUGIN_VERSION', '1.0.0')
            description = getattr(plugin_class, 'PLUGIN_DESCRIPTION',
                                  plugin_class.__doc__ or '')
            author = getattr(plugin_class, 'PLUGIN_AUTHOR', '')
            license = getattr(plugin_class, 'PLUGIN_LICENSE', '')
            dependencies = getattr(plugin_class, 'PLUGIN_DEPENDENCIES', [])

            return PluginMetadata(
                name=name,
                version=version,
                description=description.strip(),
                author=author,
                license=license,
                dependencies=dependencies,
                file_path=str(file_path),
                class_name=class_name
            )

        except Exception as e:
            self.logger.error(f"Error extracting metadata from {file_path}: {e}")
            return None

    async def load_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Load and instantiate a plugin."""
        metadata = self.registry.get_plugin(plugin_name)
        if not metadata:
            raise ValueError(f"Plugin not found in registry: {plugin_name}")

        if not metadata.is_enabled:
            raise ValueError(f"Plugin is disabled: {plugin_name}")

        # Check if already loaded
        if plugin_name in self._plugin_instances:
            self.logger.info(f"Plugin {plugin_name} already loaded")
            return self._plugin_instances[plugin_name]

        try:
            self.logger.info(f"Loading plugin: {plugin_name}")

            # Validate dependencies
            dep_errors = self.registry.validate_dependencies(plugin_name)
            if dep_errors:
                raise ValueError(f"Dependency validation failed: {', '.join(dep_errors)}")

            # Load dependencies first
            for dep_name in self.registry.get_plugin_dependencies(plugin_name):
                if dep_name not in self._plugin_instances:
                    await self.load_plugin(dep_name)

            # Load the plugin module
            module = await self._load_module(metadata)

            # Get the plugin class
            plugin_class = getattr(module, metadata.class_name)

            # Instantiate the plugin
            plugin_instance = plugin_class()

            # Initialize the plugin
            await plugin_instance.initialize(config or {})

            # Store the instance
            self._plugin_instances[plugin_name] = plugin_instance

            # Update registry stats
            self.registry.update_load_stats(plugin_name, True)

            self.logger.info(f"Plugin {plugin_name} loaded successfully")
            return plugin_instance

        except Exception as e:
            self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
            self.registry.update_load_stats(plugin_name, False)
            raise

    async def _load_module(self, metadata: PluginMetadata) -> Any:
        """Load a plugin module from file."""
        file_path = Path(metadata.file_path)
        module_name = f"plugin_{metadata.name}_{metadata.version}"

        # Check if already loaded
        if module_name in self._loaded_modules:
            return self._loaded_modules[module_name]

        try:
            # Load the module
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if not spec or not spec.loader:
                raise ImportError(f"Cannot load module from {file_path}")

            module = importlib.util.module_from_spec(spec)

            # Add to sys.modules for proper import handling
            sys.modules[module_name] = module

            # Execute the module
            spec.loader.exec_module(module)

            # Store the loaded module
            self._loaded_modules[module_name] = module

            return module

        except Exception as e:
            # Clean up on failure
            if module_name in sys.modules:
                del sys.modules[module_name]
            raise ImportError(f"Failed to load module {module_name}: {e}")

    async def unload_plugin(self, plugin_name: str) -> None:
        """Unload a plugin and clean up resources."""
        if plugin_name not in self._plugin_instances:
            self.logger.warning(f"Plugin {plugin_name} not loaded")
            return

        try:
            self.logger.info(f"Unloading plugin: {plugin_name}")

            plugin_instance = self._plugin_instances[plugin_name]

            # Cleanup the plugin
            await plugin_instance.cleanup()

            # Remove from instances
            del self._plugin_instances[plugin_name]

            self.logger.info(f"Plugin {plugin_name} unloaded successfully")

        except Exception as e:
            self.logger.error(f"Error unloading plugin {plugin_name}: {e}")

    async def reload_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Reload a plugin (unload and load again)."""
        await self.unload_plugin(plugin_name)
        return await self.load_plugin(plugin_name, config)

    def get_loaded_plugins(self) -> Dict[str, PluginInterface]:
        """Get all currently loaded plugin instances."""
        return self._plugin_instances.copy()

    def is_plugin_loaded(self, plugin_name: str) -> bool:
        """Check if a plugin is currently loaded."""
        return plugin_name in self._plugin_instances

    async def cleanup_all(self) -> None:
        """Cleanup all loaded plugins."""
        self.logger.info("Cleaning up all loaded plugins")

        for plugin_name in list(self._plugin_instances.keys()):
            await self.unload_plugin(plugin_name)

        # Clear module cache
        for module_name in list(self._loaded_modules.keys()):
            if module_name in sys.modules:
                del sys.modules[module_name]

        self._loaded_modules.clear()
        self.logger.info("All plugins cleaned up")