"""
Robust code generation system that follows established patterns and includes validation.
"""

import ast
import logging
import re
from typing import Dict, List, Any, Optional, Tu<PERSON>, Set
from dataclasses import dataclass
from pathlib import Path
import inspect
import textwrap

from .code_analysis import CodeA<PERSON>yzer, CodeContext
from .code_editor import CodeEditor
from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class CodePattern:
    """Represents a code pattern found in the codebase."""
    pattern_type: str
    name: str
    template: str
    examples: List[str]
    usage_frequency: int
    confidence: float


@dataclass
class GenerationContext:
    """Context for code generation."""
    target_file: str
    existing_patterns: List[CodePattern]
    dependencies: Set[str]
    style_guide: Dict[str, Any]
    constraints: List[str]
    requirements: Dict[str, Any]


@dataclass
class GeneratedCode:
    """Represents generated code with metadata."""
    code: str
    language: str
    pattern_used: Optional[str]
    dependencies: List[str]
    tests: Optional[str]
    documentation: Optional[str]
    validation_results: Dict[str, Any]


class RobustCodeGenerator:
    """Robust code generator that follows established patterns."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.analyzer = CodeAnalyzer()
        self.editor = CodeEditor()
        self._pattern_cache: Dict[str, List[CodePattern]] = {}
        self._style_guides: Dict[str, Dict[str, Any]] = {}
        
    async def analyze_codebase_patterns(self, codebase_paths: List[str]) -> Dict[str, List[CodePattern]]:
        """
        Analyze codebase to identify common patterns.
        
        Args:
            codebase_paths: List of paths to analyze
            
        Returns:
            Dictionary of patterns by category
        """
        patterns = {
            "class_patterns": [],
            "function_patterns": [],
            "import_patterns": [],
            "error_handling_patterns": [],
            "testing_patterns": []
        }
        
        try:
            for path in codebase_paths:
                if Path(path).suffix == '.py':
                    with open(path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    context = await self.analyzer.analyze_code_comprehensive(content, path)
                    
                    # Extract class patterns
                    patterns["class_patterns"].extend(
                        self._extract_class_patterns(context, content)
                    )
                    
                    # Extract function patterns
                    patterns["function_patterns"].extend(
                        self._extract_function_patterns(context, content)
                    )
                    
                    # Extract import patterns
                    patterns["import_patterns"].extend(
                        self._extract_import_patterns(context, content)
                    )
                    
                    # Extract error handling patterns
                    patterns["error_handling_patterns"].extend(
                        self._extract_error_handling_patterns(context, content)
                    )
            
            # Cache patterns
            self._pattern_cache.update(patterns)
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
            return patterns
    
    async def generate_code(self, 
                           requirements: Dict[str, Any],
                           generation_context: GenerationContext) -> GeneratedCode:
        """
        Generate code based on requirements and context.
        
        Args:
            requirements: Code generation requirements
            generation_context: Context for generation
            
        Returns:
            Generated code with metadata
        """
        try:
            # Determine the best pattern to use
            selected_pattern = self._select_best_pattern(requirements, generation_context)
            
            # Generate the core code
            code = await self._generate_core_code(requirements, selected_pattern, generation_context)
            
            # Add error handling
            code = self._add_error_handling(code, generation_context)
            
            # Add documentation
            documentation = self._generate_documentation(code, requirements)
            
            # Generate tests
            tests = await self._generate_tests(code, requirements)
            
            # Validate generated code
            validation_results = await self._validate_generated_code(
                code, generation_context
            )
            
            # Determine dependencies
            dependencies = self._extract_dependencies(code)
            
            return GeneratedCode(
                code=code,
                language="python",  # Assuming Python for now
                pattern_used=selected_pattern.name if selected_pattern else None,
                dependencies=dependencies,
                tests=tests,
                documentation=documentation,
                validation_results=validation_results
            )
            
        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return GeneratedCode(
                code=f"# Code generation failed: {str(e)}",
                language="python",
                pattern_used=None,
                dependencies=[],
                tests=None,
                documentation=None,
                validation_results={"error": str(e)}
            )
    
    def _extract_class_patterns(self, context: CodeContext, content: str) -> List[CodePattern]:
        """Extract class patterns from code context."""
        patterns = []
        
        for class_info in context.classes:
            # Identify common class patterns
            if self._is_data_class_pattern(class_info):
                patterns.append(CodePattern(
                    pattern_type="class",
                    name="dataclass",
                    template=self._create_dataclass_template(class_info),
                    examples=[self._extract_class_code(content, class_info)],
                    usage_frequency=1,
                    confidence=0.8
                ))
            
            if self._is_singleton_pattern(class_info):
                patterns.append(CodePattern(
                    pattern_type="class",
                    name="singleton",
                    template=self._create_singleton_template(class_info),
                    examples=[self._extract_class_code(content, class_info)],
                    usage_frequency=1,
                    confidence=0.7
                ))
        
        return patterns
    
    def _extract_function_patterns(self, context: CodeContext, content: str) -> List[CodePattern]:
        """Extract function patterns from code context."""
        patterns = []
        
        for func_info in context.functions:
            # Identify async function pattern
            if self._is_async_function(func_info, content):
                patterns.append(CodePattern(
                    pattern_type="function",
                    name="async_function",
                    template=self._create_async_function_template(func_info),
                    examples=[self._extract_function_code(content, func_info)],
                    usage_frequency=1,
                    confidence=0.9
                ))
            
            # Identify property pattern
            if self._is_property_function(func_info):
                patterns.append(CodePattern(
                    pattern_type="function",
                    name="property",
                    template=self._create_property_template(func_info),
                    examples=[self._extract_function_code(content, func_info)],
                    usage_frequency=1,
                    confidence=0.8
                ))
        
        return patterns
    
    def _extract_import_patterns(self, context: CodeContext, content: str) -> List[CodePattern]:
        """Extract import patterns from code context."""
        patterns = []
        
        # Group imports by type
        standard_imports = []
        third_party_imports = []
        local_imports = []
        
        for imp in context.imports:
            if self._is_standard_library(imp):
                standard_imports.append(imp)
            elif self._is_local_import(imp):
                local_imports.append(imp)
            else:
                third_party_imports.append(imp)
        
        if standard_imports or third_party_imports or local_imports:
            patterns.append(CodePattern(
                pattern_type="import",
                name="import_organization",
                template=self._create_import_template(
                    standard_imports, third_party_imports, local_imports
                ),
                examples=[self._extract_import_section(content)],
                usage_frequency=1,
                confidence=0.9
            ))
        
        return patterns
    
    def _extract_error_handling_patterns(self, context: CodeContext, content: str) -> List[CodePattern]:
        """Extract error handling patterns from code context."""
        patterns = []
        
        # Look for try-except patterns in the AST
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Try):
                    pattern_code = ast.unparse(node)
                    patterns.append(CodePattern(
                        pattern_type="error_handling",
                        name="try_except",
                        template=self._create_try_except_template(node),
                        examples=[pattern_code],
                        usage_frequency=1,
                        confidence=0.8
                    ))
        except:
            pass
        
        return patterns
    
    def _select_best_pattern(self, 
                           requirements: Dict[str, Any], 
                           context: GenerationContext) -> Optional[CodePattern]:
        """Select the best pattern for the requirements."""
        requirement_type = requirements.get("type", "function")
        
        # Get patterns for the requirement type
        patterns = context.existing_patterns
        
        if not patterns:
            return None
        
        # Score patterns based on requirements
        scored_patterns = []
        for pattern in patterns:
            score = self._score_pattern(pattern, requirements, context)
            scored_patterns.append((pattern, score))
        
        # Return the highest scoring pattern
        if scored_patterns:
            return max(scored_patterns, key=lambda x: x[1])[0]
        
        return None
    
    async def _generate_core_code(self, 
                                requirements: Dict[str, Any],
                                pattern: Optional[CodePattern],
                                context: GenerationContext) -> str:
        """Generate the core code structure."""
        requirement_type = requirements.get("type", "function")
        name = requirements.get("name", "generated_code")
        
        if pattern:
            # Use pattern template
            code = self._apply_pattern_template(pattern, requirements)
        else:
            # Generate from scratch
            if requirement_type == "class":
                code = self._generate_class_from_scratch(requirements)
            elif requirement_type == "function":
                code = self._generate_function_from_scratch(requirements)
            else:
                code = f"# Generated code for {name}\npass"
        
        return code
    
    def _add_error_handling(self, code: str, context: GenerationContext) -> str:
        """Add appropriate error handling to the code."""
        # Find error handling patterns in context
        error_patterns = [p for p in context.existing_patterns 
                         if p.pattern_type == "error_handling"]
        
        if not error_patterns:
            # Add basic error handling
            lines = code.split('\n')
            
            # Find function definitions and add try-except
            enhanced_lines = []
            in_function = False
            indent_level = 0
            
            for line in lines:
                enhanced_lines.append(line)
                
                if line.strip().startswith('def ') or line.strip().startswith('async def '):
                    in_function = True
                    indent_level = len(line) - len(line.lstrip())
                elif in_function and line.strip() and not line.startswith(' ' * (indent_level + 4)):
                    in_function = False
                elif in_function and 'raise' not in line and 'try:' not in line:
                    # Add basic error handling suggestion
                    if line.strip() and not line.strip().startswith('#'):
                        enhanced_lines.append(f"{' ' * (indent_level + 4)}# TODO: Add error handling")
            
            return '\n'.join(enhanced_lines)
        
        return code
    
    def _generate_documentation(self, code: str, requirements: Dict[str, Any]) -> str:
        """Generate documentation for the code."""
        name = requirements.get("name", "generated_code")
        description = requirements.get("description", "Generated code")
        
        doc = f"""
# {name}

{description}

## Usage

```python
{code}
```

## Parameters

{self._extract_parameters_doc(code)}

## Returns

{self._extract_returns_doc(code)}
"""
        return doc.strip()
    
    async def _generate_tests(self, code: str, requirements: Dict[str, Any]) -> str:
        """Generate unit tests for the code."""
        name = requirements.get("name", "generated_code")
        
        # Extract functions and classes from code
        try:
            tree = ast.parse(code)
            functions = []
            classes = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)
            
            test_code = f"""
import pytest
from unittest.mock import Mock, patch

# Import the code under test
# from your_module import {', '.join(functions + classes)}


class Test{name.title()}:
    \"\"\"Test cases for {name}.\"\"\"
    
    def setup_method(self):
        \"\"\"Set up test fixtures.\"\"\"
        pass
    
    def teardown_method(self):
        \"\"\"Clean up after tests.\"\"\"
        pass
"""
            
            # Generate test methods for functions
            for func in functions:
                test_code += f"""
    def test_{func}_success(self):
        \"\"\"Test {func} with valid input.\"\"\"
        # Arrange
        # TODO: Set up test data
        
        # Act
        # result = {func}()
        
        # Assert
        # assert result is not None
        pass
    
    def test_{func}_error_handling(self):
        \"\"\"Test {func} error handling.\"\"\"
        # TODO: Test error conditions
        pass
"""
            
            return test_code.strip()
            
        except Exception as e:
            return f"# Test generation failed: {str(e)}\n# TODO: Write tests manually"
    
    async def _validate_generated_code(self, 
                                     code: str, 
                                     context: GenerationContext) -> Dict[str, Any]:
        """Validate the generated code."""
        validation_results = {
            "syntax_valid": False,
            "style_compliant": False,
            "dependencies_satisfied": False,
            "issues": [],
            "warnings": []
        }
        
        try:
            # Check syntax
            ast.parse(code)
            validation_results["syntax_valid"] = True
        except SyntaxError as e:
            validation_results["issues"].append(f"Syntax error: {str(e)}")
        
        # Check style compliance
        style_issues = self._check_style_compliance(code, context.style_guide)
        if style_issues:
            validation_results["warnings"].extend(style_issues)
        else:
            validation_results["style_compliant"] = True
        
        # Check dependencies
        code_deps = self._extract_dependencies(code)
        missing_deps = [dep for dep in code_deps if dep not in context.dependencies]
        if missing_deps:
            validation_results["warnings"].append(f"Missing dependencies: {missing_deps}")
        else:
            validation_results["dependencies_satisfied"] = True
        
        return validation_results
    
    # Helper methods
    def _is_data_class_pattern(self, class_info: Dict[str, Any]) -> bool:
        """Check if class follows dataclass pattern."""
        decorators = class_info.get("decorators", [])
        return any("dataclass" in dec for dec in decorators)
    
    def _is_singleton_pattern(self, class_info: Dict[str, Any]) -> bool:
        """Check if class follows singleton pattern."""
        methods = class_info.get("methods", [])
        return any(method["name"] == "__new__" for method in methods)
    
    def _is_async_function(self, func_info: Dict[str, Any], content: str) -> bool:
        """Check if function is async."""
        # Look for async keyword in the function definition
        lines = content.split('\n')
        if func_info.get("line_number"):
            line = lines[func_info["line_number"] - 1]
            return "async def" in line
        return False
    
    def _is_property_function(self, func_info: Dict[str, Any]) -> bool:
        """Check if function is a property."""
        decorators = func_info.get("decorators", [])
        return any("property" in dec for dec in decorators)
    
    def _is_standard_library(self, import_name: str) -> bool:
        """Check if import is from standard library."""
        stdlib_modules = {
            'os', 'sys', 'json', 'datetime', 'collections', 'itertools',
            'functools', 'operator', 're', 'math', 'random', 'string',
            'pathlib', 'typing', 'dataclasses', 'abc', 'asyncio'
        }
        return import_name.split('.')[0] in stdlib_modules
    
    def _is_local_import(self, import_name: str) -> bool:
        """Check if import is local (relative import)."""
        return import_name.startswith('.') or 'agent_framework' in import_name
    
    def _score_pattern(self, 
                      pattern: CodePattern, 
                      requirements: Dict[str, Any],
                      context: GenerationContext) -> float:
        """Score a pattern for the given requirements."""
        score = pattern.confidence
        
        # Boost score for matching pattern type
        if pattern.pattern_type == requirements.get("type", "function"):
            score += 0.3
        
        # Boost score for usage frequency
        score += min(0.2, pattern.usage_frequency * 0.05)
        
        return score
    
    def _apply_pattern_template(self, pattern: CodePattern, requirements: Dict[str, Any]) -> str:
        """Apply pattern template with requirements."""
        template = pattern.template
        name = requirements.get("name", "generated_item")
        
        # Simple template substitution
        template = template.replace("{name}", name)
        template = template.replace("{description}", requirements.get("description", ""))
        
        return template
    
    def _generate_class_from_scratch(self, requirements: Dict[str, Any]) -> str:
        """Generate a class from scratch."""
        name = requirements.get("name", "GeneratedClass")
        description = requirements.get("description", "Generated class")
        
        return f'''class {name}:
    """
    {description}
    """
    
    def __init__(self):
        """Initialize the {name}."""
        pass
    
    def __str__(self) -> str:
        """String representation."""
        return f"{name}()"
    
    def __repr__(self) -> str:
        """Developer representation."""
        return self.__str__()'''
    
    def _generate_function_from_scratch(self, requirements: Dict[str, Any]) -> str:
        """Generate a function from scratch."""
        name = requirements.get("name", "generated_function")
        description = requirements.get("description", "Generated function")
        is_async = requirements.get("async", False)
        
        func_def = "async def" if is_async else "def"
        
        return f'''{func_def} {name}():
    """
    {description}
    
    Returns:
        None: TODO: Define return type
    """
    # TODO: Implement function logic
    pass'''
    
    def _extract_dependencies(self, code: str) -> List[str]:
        """Extract dependencies from code."""
        dependencies = []
        
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        dependencies.append(node.module)
        except:
            pass
        
        return dependencies
    
    def _check_style_compliance(self, code: str, style_guide: Dict[str, Any]) -> List[str]:
        """Check code style compliance."""
        issues = []
        
        lines = code.split('\n')
        
        # Check line length
        max_line_length = style_guide.get("max_line_length", 88)
        for i, line in enumerate(lines, 1):
            if len(line) > max_line_length:
                issues.append(f"Line {i} exceeds maximum length ({len(line)} > {max_line_length})")
        
        # Check for trailing whitespace
        for i, line in enumerate(lines, 1):
            if line.rstrip() != line:
                issues.append(f"Line {i} has trailing whitespace")
        
        return issues
    
    def _extract_parameters_doc(self, code: str) -> str:
        """Extract parameters documentation from code."""
        # Simplified parameter extraction
        return "TODO: Document parameters"
    
    def _extract_returns_doc(self, code: str) -> str:
        """Extract returns documentation from code."""
        # Simplified returns extraction
        return "TODO: Document return value"
    
    # Template creation methods
    def _create_dataclass_template(self, class_info: Dict[str, Any]) -> str:
        """Create dataclass template."""
        return '''from dataclasses import dataclass

@dataclass
class {name}:
    """
    {description}
    """
    pass'''
    
    def _create_singleton_template(self, class_info: Dict[str, Any]) -> str:
        """Create singleton template."""
        return '''class {name}:
    """
    {description}
    """
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance'''
    
    def _create_async_function_template(self, func_info: Dict[str, Any]) -> str:
        """Create async function template."""
        return '''async def {name}():
    """
    {description}
    """
    pass'''
    
    def _create_property_template(self, func_info: Dict[str, Any]) -> str:
        """Create property template."""
        return '''@property
def {name}(self):
    """
    {description}
    """
    return self._{name}'''
    
    def _create_import_template(self, std_imports: List[str], 
                              third_party: List[str], 
                              local: List[str]) -> str:
        """Create import template."""
        template = ""
        
        if std_imports:
            template += "\n".join(f"import {imp}" for imp in std_imports) + "\n\n"
        
        if third_party:
            template += "\n".join(f"import {imp}" for imp in third_party) + "\n\n"
        
        if local:
            template += "\n".join(f"from {imp}" for imp in local) + "\n"
        
        return template.strip()
    
    def _create_try_except_template(self, node: ast.Try) -> str:
        """Create try-except template."""
        return '''try:
    # {operation}
    pass
except Exception as e:
    # Handle error
    pass'''
    
    def _extract_class_code(self, content: str, class_info: Dict[str, Any]) -> str:
        """Extract class code from content."""
        lines = content.split('\n')
        start_line = class_info.get("line_number", 1) - 1
        
        # Find end of class (simplified)
        end_line = start_line + 10  # Default to 10 lines
        for i in range(start_line + 1, len(lines)):
            if lines[i] and not lines[i].startswith(' ') and not lines[i].startswith('\t'):
                end_line = i
                break
        
        return '\n'.join(lines[start_line:end_line])
    
    def _extract_function_code(self, content: str, func_info: Dict[str, Any]) -> str:
        """Extract function code from content."""
        lines = content.split('\n')
        start_line = func_info.get("line_number", 1) - 1
        
        # Find end of function (simplified)
        end_line = start_line + 5  # Default to 5 lines
        for i in range(start_line + 1, len(lines)):
            if lines[i] and not lines[i].startswith(' ') and not lines[i].startswith('\t'):
                end_line = i
                break
        
        return '\n'.join(lines[start_line:end_line])
    
    def _extract_import_section(self, content: str) -> str:
        """Extract import section from content."""
        lines = content.split('\n')
        import_lines = []
        
        for line in lines:
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                import_lines.append(line)
            elif import_lines and line.strip() == '':
                continue
            elif import_lines:
                break
        
        return '\n'.join(import_lines)
