"""
Advanced Agent Orchestrator with comprehensive coding capabilities.

This module provides the AdvancedAgentOrchestrator class which extends the base
AgentOrchestrator with advanced features including:
- Automatic bug fixing loops
- Comprehensive evaluation cycles  
- Advanced code generation and analysis
- Intelligent debugging and error resolution
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from .code_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .code_editor import CodeEditor
from .debugger import Debugger
from .robust_code_generator import RobustCodeGenerator
from .automatic_bug_fix_loop import Automatic<PERSON><PERSON><PERSON><PERSON><PERSON>oop
from .automatic_evaluation_cycles import AutomaticEvaluationCycles
from ..agents.testing_agent import TestingAgent
from ..agents.code_analysis_agent import CodeAnalysisAgent
from ..core.orchestrator import AgentOrchestrator
from ..core.config import FrameworkConfig  # Ensure we can build config when path is provided
from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class AgentCapabilities:
    """Agent capabilities configuration."""
    enable_automatic_bug_fixing: bool = True
    enable_automatic_evaluation: bool = True
    enable_advanced_code_generation: bool = True
    enable_comprehensive_testing: bool = True
    max_fix_iterations: int = 5
    evaluation_on_every_change: bool = True
    rollback_on_critical_issues: bool = True


class AdvancedAgentOrchestrator(AgentOrchestrator):
    """Advanced orchestrator with comprehensive coding capabilities."""
    
    def __init__(self, config_path: Optional[str] = None, capabilities: Optional[AgentCapabilities] = None):
        # Ensure base orchestrator gets a FrameworkConfig, not a path
        if isinstance(config_path, str) or config_path is None:
            # Build a FrameworkConfig from path or use defaults
            config = FrameworkConfig.from_file(config_path) if config_path else FrameworkConfig()
            super().__init__(config)
        else:
            # If caller passed a FrameworkConfig mistakenly, still support it
            super().__init__(config_path)  # type: ignore[arg-type]

        self.capabilities = capabilities or AgentCapabilities()
        self.logger = logging.getLogger(__name__)

        # Initialize advanced components
        self.code_analyzer = CodeAnalyzer()
        self.code_editor = CodeEditor()
        self.debugger = Debugger()
        self.code_generator = RobustCodeGenerator()

        if self.capabilities.enable_automatic_bug_fixing:
            self.bug_fix_loop = AutomaticBugFixLoop(
                max_iterations=self.capabilities.max_fix_iterations
            )

        if self.capabilities.enable_automatic_evaluation:
            self.evaluation_cycles = AutomaticEvaluationCycles()

        # Specialized agents - provide a default model config
        from ..core.config import ModelConfig
        default_model_config = ModelConfig()
        self.testing_agent = TestingAgent(global_model_config=default_model_config)
        self.code_analysis_agent = CodeAnalysisAgent(global_model_config=default_model_config)

        # Initialize the base orchestrator asynchronously when needed
        self._base_initialized = False

    async def _ensure_initialized(self):
        """Ensure the base orchestrator is initialized."""
        if not self._base_initialized:
            try:
                await super().initialize()

                # Initialize specialized agents
                await self.testing_agent.initialize({})
                await self.code_analysis_agent.initialize({})

                self._base_initialized = True
            except Exception as e:
                # If base initialization fails, we can still work with advanced components
                self.logger.warning(f"Base orchestrator initialization failed: {e}")
                self._base_initialized = True  # Mark as initialized to avoid repeated attempts
    
    def _to_code_str(self, generated: Any) -> str:
        """Normalize generated result to string code."""
        code = getattr(generated, "code", None)
        if isinstance(code, str):
            return code
        if isinstance(generated, str):
            return generated
        return str(generated)
        
    async def advanced_code_implementation(self, 
                                         requirements: Dict[str, Any],
                                         file_path: str,
                                         existing_code: Optional[str] = None) -> Dict[str, Any]:
        """
        Implement code with advanced capabilities including validation and testing.
        
        Args:
            requirements: Code implementation requirements
            file_path: Target file path
            existing_code: Existing code content if any
            
        Returns:
            Implementation results with comprehensive feedback
        """
        self.logger.info(f"Starting advanced code implementation for {file_path}")
        
        try:
            # Step 1: Analyze existing code if provided
            context = None
            if existing_code:
                context = await self.code_analyzer.analyze_code_comprehensive(
                    existing_code, file_path
                )
            
            # Step 2: Prepare for generation
            preparation = await self._prepare_code_generation(requirements, file_path, context)
            
            # Check safety score
            safety_score = preparation.get("safety_score", 0.8)
            if safety_score < 0.6:
                self.logger.warning("Low safety score detected, proceeding with caution")
            
            # Step 3: Generate robust code
            if self.capabilities.enable_advanced_code_generation:
                # Avoid importing unknown symbol GenerationContext; pass a plain context dict
                generation_context: Dict[str, Any] = {
                    "target_file": file_path,
                    "existing_patterns": [],
                    "dependencies": preparation.get("dependencies", set()),
                    "style_guide": {},
                    "constraints": [],
                    "requirements": requirements,
                }
                
                generated_code = await self.code_generator.generate_code(
                    requirements, generation_context
                )
                
                self.logger.info("Generated code using established patterns")
            else:
                # Fallback to basic generation
                generated_code = self._basic_code_generation(requirements)
            
            # Step 4: Validate generated code
            validation_result = await self._validate_generated_code(
                generated_code, requirements, file_path
            )
            
            # Step 5: Run automatic bug fixing if enabled and issues found
            final_code = generated_code
            if (self.capabilities.enable_automatic_bug_fixing and 
                not validation_result.get("is_valid", True)):
                
                self.logger.info("Running automatic bug fixing on generated code")
                bug_fix_result = await self.bug_fix_loop.start_fix_loop(
                    error=Exception("Validation failed"),
                    code_content=self._to_code_str(generated_code),
                    file_path=file_path
                )
                
                if bug_fix_result.final_status.value == "success":
                    # Get the final fixed code from the last successful attempt
                    if bug_fix_result.fix_attempts:
                        final_code = bug_fix_result.fix_attempts[-1].fixed_code
                    self.logger.info("Bug fixing completed successfully")
                else:
                    self.logger.warning("Bug fixing did not fully resolve issues")
            
            # Step 6: Run evaluation cycles if enabled
            evaluation_result = None
            if self.capabilities.enable_automatic_evaluation:
                evaluation_result = await self.evaluation_cycles.run_evaluation_cycle(
                    self._to_code_str(final_code), file_path
                )
                
                # Check for critical issues
                if (evaluation_result and 
                    hasattr(evaluation_result, 'critical_issues') and 
                    evaluation_result.critical_issues and
                    self.capabilities.rollback_on_critical_issues):
                    
                    self.logger.warning("Critical issues detected, considering rollback")
                    # For now, we'll proceed but flag the issues
            
            # Step 7: Comprehensive testing if enabled
            test_results = None
            if self.capabilities.enable_comprehensive_testing:
                test_results = await self._run_comprehensive_tests(
                    final_code, file_path, requirements
                )
            
            return {
                "success": True,
                "generated_code": final_code,
                "validation": validation_result,
                "bug_fix_session": getattr(bug_fix_result, 'session_id', None) if 'bug_fix_result' in locals() else None,
                "evaluation": evaluation_result,
                "test_results": test_results,
                "safety_score": safety_score,
                "preparation": preparation
            }
            
        except Exception as e:
            self.logger.error(f"Advanced code implementation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "generated_code": None,
                "validation": None,
                "evaluation": None,
                "test_results": None
            }
    
    async def _prepare_code_generation(self, 
                                     requirements: Dict[str, Any], 
                                     file_path: str,
                                     context: Optional[Any] = None) -> Dict[str, Any]:
        """Prepare for code generation by analyzing requirements and context."""
        preparation = {
            "dependencies": set(),
            "constraints": [],
            "style_guide": {},
            "safety_score": 0.8,
            "patterns": []
        }
        
        # Analyze requirements
        if "dependencies" in requirements:
            preparation["dependencies"].update(requirements["dependencies"])
        
        # Add context-based insights
        if context:
            if hasattr(context, 'dependencies'):
                preparation["dependencies"].update(context.dependencies)
            if hasattr(context, 'patterns'):
                preparation["patterns"].extend(context.patterns)
        
        # Calculate safety score based on requirements complexity
        complexity_factors = [
            "file_operations" in str(requirements),
            "network_requests" in str(requirements), 
            "database" in str(requirements),
            "subprocess" in str(requirements)
        ]
        
        safety_score = 1.0 - (sum(complexity_factors) * 0.1)
        preparation["safety_score"] = max(0.3, safety_score)
        
        return preparation
    
    def _basic_code_generation(self, requirements: Dict[str, Any]) -> str:
        """Fallback basic code generation."""
        # Simple template-based generation
        function_name = requirements.get("function_name", "generated_function")
        description = requirements.get("description", "Generated function")
        
        return f'''def {function_name}():
    """
    {description}
    
    This is a basic generated function.
    """
    # TODO: Implement functionality
    pass
'''
    
    async def _validate_generated_code(self, 
                                     generated_code: Any, 
                                     requirements: Dict[str, Any],
                                     file_path: str) -> Dict[str, Any]:
        """Validate generated code against requirements."""
        try:
            code_str = self._to_code_str(generated_code)
            
            # Basic syntax validation
            import ast
            ast.parse(code_str)
            
            # Check if requirements are met (basic check)
            validation_result = {
                "is_valid": True,
                "syntax_valid": True,
                "requirements_met": True,
                "issues": []
            }
            
            # Additional validation logic can be added here
            
            return validation_result
            
        except SyntaxError as e:
            return {
                "is_valid": False,
                "syntax_valid": False,
                "requirements_met": False,
                "issues": [f"Syntax error: {e}"]
            }
        except Exception as e:
            return {
                "is_valid": False,
                "syntax_valid": True,
                "requirements_met": False,
                "issues": [f"Validation error: {e}"]
            }
    
    async def _run_comprehensive_tests(self,
                                     code: Any,
                                     file_path: str,
                                     requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive tests on the generated code."""
        # This would integrate with the testing agent
        # For now, return a basic test result
        return {
            "tests_run": 0,
            "tests_passed": 0,
            "coverage": 0.0,
            "test_files_generated": []
        }

    async def comprehensive_code_analysis(self,
                                        code_content: str,
                                        file_path: str,
                                        analysis_depth: str = "comprehensive") -> Dict[str, Any]:
        """
        Perform comprehensive code analysis with advanced capabilities.

        Args:
            code_content: Code to analyze
            file_path: Path to the code file
            analysis_depth: Depth of analysis (basic, comprehensive, deep)

        Returns:
            Comprehensive analysis results
        """
        self.logger.info(f"Starting comprehensive code analysis for {file_path}")

        # Ensure base orchestrator is initialized if needed
        await self._ensure_initialized()

        try:
            # Advanced code analysis
            context = await self.code_analyzer.analyze_code_comprehensive(
                code_content, file_path, include_relationships=True
            )

            # Run evaluation cycles
            if self.capabilities.enable_automatic_evaluation:
                evaluation = await self.evaluation_cycles.run_evaluation_cycle(
                    code_content, file_path
                )
            else:
                evaluation = None

            # Generate improvement suggestions
            suggestions: List[str] = []

            if context.potential_issues:
                suggestions.extend([
                    f"Address {issue['type']}: {issue['message']}"
                    for issue in context.potential_issues
                ])

            if evaluation and getattr(evaluation, "improvement_plan", None):
                suggestions.extend(evaluation.improvement_plan)

            # Test coverage analysis
            if self.capabilities.enable_comprehensive_testing:
                # Pass a proper list of source files; ensure test runner argument shape matches signature
                test_analysis = await self.testing_agent.analyze_test_coverage(
                    [file_path], ["pytest"]
                )
            else:
                test_analysis = None

            return {
                "success": True,
                "context": context,
                "evaluation": evaluation,
                "test_analysis": test_analysis,
                "suggestions": suggestions,
                "quality_score": getattr(evaluation, "overall_score", 0.0) if evaluation else 0.0
            }

        except Exception as e:
            self.logger.error(f"Comprehensive code analysis failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def intelligent_code_refactoring(self,
                                         code_content: str,
                                         file_path: str,
                                         refactoring_goals: List[str]) -> Dict[str, Any]:
        """
        Perform intelligent code refactoring with validation and testing.

        Args:
            code_content: Code to refactor
            file_path: Path to the code file
            refactoring_goals: List of refactoring objectives

        Returns:
            Refactoring results
        """
        self.logger.info(f"Starting intelligent code refactoring for {file_path}")

        try:
            # Analyze current code
            original_context = await self.code_analyzer.analyze_code_comprehensive(
                code_content, file_path
            )

            # Generate refactored code based on goals
            refactoring_requirements = {
                "type": "refactoring",
                "name": "code_refactoring",
                "description": f"Refactor code to achieve: {', '.join(refactoring_goals)}",
                "goals": refactoring_goals,
                "original_code": code_content
            }

            # Use code generator for refactoring
            if self.capabilities.enable_advanced_code_generation:
                # Avoid importing unknown GenerationContext; use dict
                generation_context: Dict[str, Any] = {
                    "target_file": file_path,
                    "existing_patterns": [],
                    "dependencies": original_context.dependencies,
                    "style_guide": {},
                    "constraints": refactoring_goals,
                    "requirements": refactoring_requirements,
                }

                refactored_code = await self.code_generator.generate_code(
                    refactoring_requirements, generation_context
                )
            else:
                # Basic refactoring
                refactored_code = self._basic_refactoring(code_content, refactoring_goals)

            # Validate refactored code
            from .code_editor import CodeEdit

            edit = CodeEdit(
                file_path=file_path,
                original_content=code_content,
                modified_content=self._to_code_str(refactored_code),
                edit_type="refactoring",
                line_range=(0, len(code_content.split('\n'))),
                description=f"Refactoring: {', '.join(refactoring_goals)}"
            )

            validation = await self.code_editor.validate_edit(edit)

            if not validation.is_valid:
                return {
                    "success": False,
                    "error": "Refactored code failed validation",
                    "validation": validation
                }

            # Evaluate refactored code
            if self.capabilities.enable_automatic_evaluation:
                evaluation = await self.evaluation_cycles.run_evaluation_cycle(
                    edit.modified_content, file_path
                )

                # Compare with original evaluation
                original_evaluation = await self.evaluation_cycles.run_evaluation_cycle(
                    code_content, file_path
                )

                improvement = evaluation.overall_score - original_evaluation.overall_score
            else:
                evaluation = None
                improvement = 0.0

            # Generate tests for refactored code
            if self.capabilities.enable_comprehensive_testing:
                test_code = await self.testing_agent.generate_unit_tests(
                    edit.modified_content, "python", "pytest"
                )
            else:
                test_code = None

            return {
                "success": True,
                "refactored_code": refactored_code,
                "validation": validation,
                "evaluation": evaluation,
                "improvement_score": improvement,
                "test_code": test_code,
                "original_context": original_context
            }

        except Exception as e:
            self.logger.error(f"Intelligent code refactoring failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _basic_refactoring(self, code_content: str, goals: List[str]) -> str:
        """Basic refactoring fallback."""
        # Add a comment about the refactoring goals
        refactoring_comment = f"# Refactored to achieve: {', '.join(goals)}\n"
        return refactoring_comment + code_content

    async def get_advanced_status(self) -> Dict[str, Any]:
        """Get status of all advanced capabilities."""
        status = {
            "advanced_capabilities": {
                "automatic_bug_fixing": self.capabilities.enable_automatic_bug_fixing,
                "automatic_evaluation": self.capabilities.enable_automatic_evaluation,
                "advanced_code_generation": self.capabilities.enable_advanced_code_generation,
                "comprehensive_testing": self.capabilities.enable_comprehensive_testing
            },
            "statistics": {}
        }

        # Add bug fix statistics
        if hasattr(self, 'bug_fix_loop'):
            status["statistics"]["bug_fix"] = self.bug_fix_loop.get_success_statistics()

        # Add evaluation trends
        if hasattr(self, 'evaluation_cycles'):
            status["statistics"]["quality_trends"] = self.evaluation_cycles.get_quality_trends()

        return status

    async def run_full_advanced_cycle(self,
                                     code_content: str,
                                     file_path: str,
                                     requirements: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a complete advanced cycle including analysis, generation, testing, and evaluation.

        Args:
            code_content: Code to enhance
            file_path: Path to the code file
            requirements: Optional enhancement requirements

        Returns:
            Complete advanced results
        """
        self.logger.info(f"Starting full advanced cycle for {file_path}")

        # Ensure base orchestrator is initialized if needed
        await self._ensure_initialized()

        results: Dict[str, Any] = {
            "analysis": None,
            "implementation": None,
            "testing": None,
            "evaluation": None,
            "overall_success": False
        }

        try:
            # Step 1: Comprehensive analysis
            analysis_result = await self.comprehensive_code_analysis(
                code_content, file_path, "comprehensive"
            )
            results["analysis"] = analysis_result

            # Step 2: Advanced implementation (if requirements provided)
            if requirements:
                implementation_result = await self.advanced_code_implementation(
                    requirements, file_path, code_content
                )
                results["implementation"] = implementation_result

                # Use the implemented code for further steps
                if implementation_result.get("success"):
                    enhanced_code = implementation_result["generated_code"]
                    if hasattr(enhanced_code, 'code'):
                        code_content = enhanced_code.code

            # Step 3: Comprehensive testing
            if self.capabilities.enable_comprehensive_testing:
                test_result = await self.testing_agent.run_comprehensive_test_suite(
                    [], [file_path], "pytest"
                )
                results["testing"] = test_result

            # Step 4: Final evaluation
            if self.capabilities.enable_automatic_evaluation:
                final_evaluation = await self.evaluation_cycles.run_evaluation_cycle(
                    code_content, file_path
                )
                results["evaluation"] = final_evaluation

                # Determine overall success
                results["overall_success"] = (
                    final_evaluation.overall_quality.value in ["excellent", "good"] and
                    not final_evaluation.rollback_recommended
                )
            else:
                results["overall_success"] = True

            self.logger.info(f"Full enhancement cycle completed: {'success' if results['overall_success'] else 'needs_improvement'}")

            return results

        except Exception as e:
            self.logger.error(f"Full enhancement cycle failed: {e}")
            results["error"] = str(e)
            return results
