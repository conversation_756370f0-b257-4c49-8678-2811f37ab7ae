"""
Enhanced message broker with typed events and improved routing.

This module extends the basic message broker with additional features
for better component integration.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable, Type, TypeVar, Generic
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import json

from ..shared.base_service import BaseService
from ..shared.data_models import UnifiedRequest, StandardResult
from ..core.config import FrameworkConfig

T = TypeVar('T')


class EventPriority(Enum):
    """Event priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TypedEvent(Generic[T]):
    """Strongly typed event with schema validation."""
    event_type: str
    payload: T
    source: str
    target: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    correlation_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "event_type": self.event_type,
            "payload": self.payload,
            "source": self.source,
            "target": self.target,
            "priority": self.priority.value,
            "correlation_id": self.correlation_id,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


@dataclass
class EventSubscription:
    """Event subscription information."""
    event_type: str
    handler: Callable[[TypedEvent], Any]
    subscriber_id: str
    filter_func: Optional[Callable[[TypedEvent], bool]] = None
    priority: int = 0  # Higher priority handlers are called first
    
    def matches(self, event: TypedEvent) -> bool:
        """Check if this subscription matches the event."""
        if self.event_type != "*" and self.event_type != event.event_type:
            return False
        
        if self.filter_func and not self.filter_func(event):
            return False
        
        return True


class MessageRouter:
    """Intelligent message routing based on content and rules."""
    
    def __init__(self):
        self.routing_rules: List[Dict[str, Any]] = []
        self.logger = logging.getLogger(__name__)
    
    def add_routing_rule(self, rule: Dict[str, Any]) -> None:
        """
        Add a routing rule.
        
        Rule format:
        {
            "condition": callable,  # Function that takes event and returns bool
            "target": str,         # Target component/agent
            "priority": int,       # Rule priority
            "transform": callable  # Optional transformation function
        }
        """
        self.routing_rules.append(rule)
        # Sort by priority (higher first)
        self.routing_rules.sort(key=lambda r: r.get("priority", 0), reverse=True)
    
    def route_event(self, event: TypedEvent) -> List[str]:
        """
        Determine routing targets for an event.
        
        Args:
            event: Event to route
            
        Returns:
            List of target component IDs
        """
        targets = []
        
        # Check explicit target first
        if event.target:
            targets.append(event.target)
        
        # Apply routing rules
        for rule in self.routing_rules:
            try:
                condition = rule.get("condition")
                if condition and condition(event):
                    target = rule.get("target")
                    if target and target not in targets:
                        targets.append(target)
            except Exception as e:
                self.logger.error(f"Error applying routing rule: {e}")
        
        return targets


class EnhancedMessageBroker(BaseService):
    """
    Enhanced message broker with typed events, routing, and persistence.
    
    Provides advanced features for component communication including:
    - Strongly typed events
    - Intelligent routing
    - Message persistence
    - Request-response patterns
    - Event filtering and transformation
    """
    
    def __init__(self, config: FrameworkConfig):
        super().__init__("enhanced_message_broker", config.model_dump() if hasattr(config, 'model_dump') else {})
        self.config = config
        
        # Event handling
        self._subscriptions: Dict[str, List[EventSubscription]] = {}
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._processor_tasks: List[asyncio.Task] = []
        
        # Routing
        self.router = MessageRouter()
        
        # Request-response handling
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._request_timeout = 30.0  # seconds
        
        # Message persistence (optional)
        self._persist_events = False
        self._event_history: List[TypedEvent] = []
        self._max_history = 1000
        
        # Metrics
        self._events_published = 0
        self._events_processed = 0
        self._events_failed = 0
    
    async def _initialize_service(self) -> None:
        """Initialize the enhanced message broker."""
        self.logger.info("Initializing enhanced message broker...")
        
        # Start event processors
        num_processors = self.get_config("num_processors", 3)
        for i in range(num_processors):
            task = self.create_background_task(self._process_events(f"processor_{i}"))
            self._processor_tasks.append(task)
        
        # Configure persistence
        self._persist_events = self.get_config("persist_events", False)
        self._max_history = self.get_config("max_event_history", 1000)
        
        # Configure request timeout
        self._request_timeout = self.get_config("request_timeout", 30.0)
        
        self.logger.info("Enhanced message broker initialized")
    
    async def _shutdown_service(self) -> None:
        """Shutdown the enhanced message broker."""
        self.logger.info("Shutting down enhanced message broker...")
        
        # Cancel processor tasks
        for task in self._processor_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self._processor_tasks:
            await asyncio.gather(*self._processor_tasks, return_exceptions=True)
        
        # Cancel pending requests
        for future in self._pending_requests.values():
            if not future.done():
                future.cancel()
        
        self.logger.info("Enhanced message broker shut down")
    
    async def publish_event(self, event: TypedEvent) -> None:
        """
        Publish a typed event.
        
        Args:
            event: Event to publish
        """
        if not self.is_running():
            self.logger.warning("Cannot publish event: broker not running")
            return
        
        try:
            # Add to queue
            await self._event_queue.put(event)
            self._events_published += 1
            
            # Add to history if persistence is enabled
            if self._persist_events:
                self._event_history.append(event)
                # Trim history if needed
                if len(self._event_history) > self._max_history:
                    self._event_history = self._event_history[-self._max_history:]
            
            self.logger.debug(f"Published event: {event.event_type} from {event.source}")
            
        except Exception as e:
            self._events_failed += 1
            self.logger.error(f"Failed to publish event: {e}")
            raise
    
    def subscribe(self, event_type: str, handler: Callable[[TypedEvent], Any],
                 subscriber_id: str, filter_func: Optional[Callable[[TypedEvent], bool]] = None,
                 priority: int = 0) -> None:
        """
        Subscribe to events with filtering and priority.
        
        Args:
            event_type: Type of events to subscribe to (or "*" for all)
            handler: Function to handle events
            subscriber_id: Unique identifier for the subscriber
            filter_func: Optional filter function
            priority: Handler priority (higher = called first)
        """
        subscription = EventSubscription(
            event_type=event_type,
            handler=handler,
            subscriber_id=subscriber_id,
            filter_func=filter_func,
            priority=priority
        )
        
        if event_type not in self._subscriptions:
            self._subscriptions[event_type] = []
        
        self._subscriptions[event_type].append(subscription)
        
        # Sort by priority (higher first)
        self._subscriptions[event_type].sort(key=lambda s: s.priority, reverse=True)
        
        self.logger.debug(f"Added subscription: {subscriber_id} -> {event_type}")
    
    def unsubscribe(self, event_type: str, subscriber_id: str) -> bool:
        """
        Unsubscribe from events.
        
        Args:
            event_type: Event type to unsubscribe from
            subscriber_id: Subscriber identifier
            
        Returns:
            True if subscription was found and removed
        """
        if event_type not in self._subscriptions:
            return False
        
        original_count = len(self._subscriptions[event_type])
        self._subscriptions[event_type] = [
            sub for sub in self._subscriptions[event_type]
            if sub.subscriber_id != subscriber_id
        ]
        
        removed = len(self._subscriptions[event_type]) < original_count
        if removed:
            self.logger.debug(f"Removed subscription: {subscriber_id} -> {event_type}")
        
        return removed
    
    async def send_request(self, request: UnifiedRequest, timeout: Optional[float] = None) -> StandardResult:
        """
        Send a request and wait for response.
        
        Args:
            request: Request to send
            timeout: Request timeout (uses default if None)
            
        Returns:
            Response result
        """
        timeout = timeout or self._request_timeout
        
        # Create response future
        response_future = asyncio.get_running_loop().create_future()
        self._pending_requests[request.id] = response_future
        
        try:
            # Publish request as event
            request_event = TypedEvent(
                event_type="request",
                payload=request,
                source="message_broker",
                target=request.target,
                correlation_id=request.correlation_id
            )
            
            await self.publish_event(request_event)
            
            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout)
            return response
            
        except asyncio.TimeoutError:
            return StandardResult.error_result(
                error="Request timeout",
                error_code="TIMEOUT"
            )
        except Exception as e:
            return StandardResult.error_result(
                error=str(e),
                error_code="REQUEST_ERROR"
            )
        finally:
            # Clean up
            self._pending_requests.pop(request.id, None)
    
    async def send_response(self, request_id: str, response: StandardResult) -> None:
        """
        Send a response to a pending request.
        
        Args:
            request_id: ID of the original request
            response: Response to send
        """
        future = self._pending_requests.get(request_id)
        if future and not future.done():
            future.set_result(response)
    
    async def _process_events(self, processor_id: str) -> None:
        """
        Process events from the queue.
        
        Args:
            processor_id: Identifier for this processor
        """
        self.logger.debug(f"Event processor {processor_id} started")
        
        while not self._shutdown_event.is_set():
            try:
                # Get event with timeout
                event = await asyncio.wait_for(
                    self._event_queue.get(),
                    timeout=1.0
                )
                
                await self._handle_event(event)
                self._events_processed += 1
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._events_failed += 1
                self.logger.error(f"Error processing event in {processor_id}: {e}")
        
        self.logger.debug(f"Event processor {processor_id} stopped")
    
    async def _handle_event(self, event: TypedEvent) -> None:
        """
        Handle a single event by notifying subscribers.
        
        Args:
            event: Event to handle
        """
        # Get all matching subscriptions
        matching_subscriptions = []
        
        # Check specific event type subscriptions
        if event.event_type in self._subscriptions:
            for subscription in self._subscriptions[event.event_type]:
                if subscription.matches(event):
                    matching_subscriptions.append(subscription)
        
        # Check wildcard subscriptions
        if "*" in self._subscriptions:
            for subscription in self._subscriptions["*"]:
                if subscription.matches(event):
                    matching_subscriptions.append(subscription)
        
        # Sort by priority
        matching_subscriptions.sort(key=lambda s: s.priority, reverse=True)
        
        # Notify subscribers
        for subscription in matching_subscriptions:
            try:
                if asyncio.iscoroutinefunction(subscription.handler):
                    await subscription.handler(event)
                else:
                    # Run sync handlers in thread pool
                    await asyncio.get_event_loop().run_in_executor(
                        None, subscription.handler, event
                    )
            except Exception as e:
                self.logger.error(
                    f"Error in event handler {subscription.subscriber_id}: {e}"
                )
    
    def get_event_history(self, event_type: Optional[str] = None,
                         limit: Optional[int] = None) -> List[TypedEvent]:
        """
        Get event history.
        
        Args:
            event_type: Filter by event type (optional)
            limit: Maximum number of events to return
            
        Returns:
            List of historical events
        """
        if not self._persist_events:
            return []
        
        events = self._event_history
        
        # Filter by event type
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        # Apply limit
        if limit:
            events = events[-limit:]
        
        return events
    
    def get_broker_stats(self) -> Dict[str, Any]:
        """Get broker statistics."""
        return {
            "events_published": self._events_published,
            "events_processed": self._events_processed,
            "events_failed": self._events_failed,
            "queue_size": self._event_queue.qsize(),
            "active_subscriptions": sum(len(subs) for subs in self._subscriptions.values()),
            "pending_requests": len(self._pending_requests),
            "event_history_size": len(self._event_history) if self._persist_events else 0
        }
