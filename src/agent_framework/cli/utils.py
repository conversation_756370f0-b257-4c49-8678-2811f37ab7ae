"""
CLI utilities for the agent framework.

Provides utility functions for CLI operations including progress indicators,
colored output, and formatting helpers with rich terminal support.
"""

import sys
import time
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional

import colorama
from colorama import Fore, Back, Style

# Import rich formatter with fallback
try:
    from .rich_formatter import RichTerminalFormatter
    _RICH_AVAILABLE = True
except ImportError:
    _RICH_AVAILABLE = False
    RichTerminalFormatter = None


class CLIUtils:
    """Utility functions for CLI operations with rich terminal support."""

    def __init__(self, use_rich: bool = True):
        """Initialize CLI utilities."""
        colorama.init(autoreset=True)

        # Initialize rich formatter if available and requested
        self.rich_formatter = None
        if use_rich and _RICH_AVAILABLE:
            try:
                self.rich_formatter = RichTerminalFormatter()
            except ImportError:
                pass

    def print_success(self, message: str) -> None:
        """Print success message in green."""
        if self.rich_formatter:
            self.rich_formatter.print_success(message)
        else:
            print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

    def print_error(self, message: str) -> None:
        """Print error message in red."""
        if self.rich_formatter:
            self.rich_formatter.print_error(message)
        else:
            print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}", file=sys.stderr)

    def print_warning(self, message: str) -> None:
        """Print warning message in yellow."""
        if self.rich_formatter:
            self.rich_formatter.print_warning(message)
        else:
            print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

    def print_info(self, message: str) -> None:
        """Print info message in blue."""
        if self.rich_formatter:
            self.rich_formatter.print_info(message)
        else:
            print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")
    
    def print_header(self, title: str, subtitle: str = None) -> None:
        """Print section header."""
        if self.rich_formatter:
            self.rich_formatter.print_header(title, subtitle)
        else:
            print(f"\n{Fore.CYAN}{Style.BRIGHT}{'=' * 60}")
            print(f"{title.center(60)}")
            if subtitle:
                print(f"{subtitle.center(60)}")
            print(f"{'=' * 60}{Style.RESET_ALL}\n")

    def print_subheader(self, title: str) -> None:
        """Print subsection header."""
        if self.rich_formatter:
            self.rich_formatter.print_section(title)
        else:
            print(f"\n{Fore.MAGENTA}{Style.BRIGHT}{title}")
            print(f"{'-' * len(title)}{Style.RESET_ALL}")

    def print_table(self, headers: List[str], rows: List[List[str]],
                   title: Optional[str] = None) -> None:
        """Print formatted table."""
        if self.rich_formatter:
            self.rich_formatter.print_table(headers, rows, title)
        else:
            if title:
                self.print_subheader(title)

            if not rows:
                self.print_info("No data to display")
                return

            # Calculate column widths
            col_widths = [len(header) for header in headers]
            for row in rows:
                for i, cell in enumerate(row):
                    if i < len(col_widths):
                        col_widths[i] = max(col_widths[i], len(str(cell)))

            # Print header
            header_row = " | ".join(
                f"{header:<{col_widths[i]}}"
                for i, header in enumerate(headers)
            )
            print(f"{Fore.CYAN}{Style.BRIGHT}{header_row}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'-' * len(header_row)}{Style.RESET_ALL}")

            # Print rows
            for row in rows:
                formatted_row = " | ".join(
                    f"{str(cell):<{col_widths[i]}}"
                    for i, cell in enumerate(row)
                )
                print(formatted_row)
    
    def print_code_block(self, code: str, language: str = "python",
                        title: Optional[str] = None) -> None:
        """Print formatted code block."""
        if self.rich_formatter:
            self.rich_formatter.print_code(code, language, title)
        else:
            if title:
                self.print_subheader(title)

            print(f"{Fore.BLACK}{Back.WHITE}```{language}")
            print(f"{code}")
            print(f"```{Style.RESET_ALL}")

    def print_json(self, data: Dict[str, Any], title: Optional[str] = None) -> None:
        """Print formatted JSON data."""
        if self.rich_formatter:
            self.rich_formatter.print_json(data, title)
        else:
            import json

            if title:
                self.print_subheader(title)

            formatted_json = json.dumps(data, indent=2, ensure_ascii=False)
            print(f"{Fore.CYAN}{formatted_json}{Style.RESET_ALL}")

    def confirm(self, message: str, default: bool = False) -> bool:
        """Ask for user confirmation."""
        if self.rich_formatter:
            return self.rich_formatter.confirm(message, default)
        else:
            default_str = "Y/n" if default else "y/N"
            response = input(f"{Fore.YELLOW}? {message} ({default_str}): {Style.RESET_ALL}")

            if not response.strip():
                return default

            return response.lower().startswith('y')

    def prompt(self, message: str, default: Optional[str] = None) -> str:
        """Prompt user for input."""
        if self.rich_formatter:
            return self.rich_formatter.prompt(message, default)
        else:
            prompt_text = f"{Fore.YELLOW}? {message}"
            if default:
                prompt_text += f" ({default})"
            prompt_text += f": {Style.RESET_ALL}"

            response = input(prompt_text)
            return response.strip() or default or ""
    
    def select_option(self, message: str, options: List[str], 
                     default: Optional[int] = None) -> int:
        """Let user select from options."""
        print(f"{Fore.YELLOW}? {message}{Style.RESET_ALL}")
        
        for i, option in enumerate(options, 1):
            marker = f"{Fore.GREEN}→{Style.RESET_ALL}" if default == i else " "
            print(f"  {marker} {i}. {option}")
        
        while True:
            try:
                prompt_text = f"{Fore.YELLOW}Select option"
                if default:
                    prompt_text += f" (default: {default})"
                prompt_text += f": {Style.RESET_ALL}"
                
                response = input(prompt_text)
                
                if not response.strip() and default:
                    return default - 1
                
                choice = int(response) - 1
                if 0 <= choice < len(options):
                    return choice
                else:
                    self.print_error(f"Please enter a number between 1 and {len(options)}")
            
            except ValueError:
                self.print_error("Please enter a valid number")
            except KeyboardInterrupt:
                raise


class ProgressIndicator:
    """Progress indicator for long-running operations with rich support."""

    def __init__(self, use_rich: bool = True):
        """Initialize progress indicator."""
        self._stop_event = threading.Event()
        self._spinner_thread = None
        self._spinner_chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"

        # Initialize rich formatter if available
        self.rich_formatter = None
        if use_rich and _RICH_AVAILABLE:
            try:
                self.rich_formatter = RichTerminalFormatter()
            except ImportError:
                pass
    
    @contextmanager
    def spinner(self, message: str):
        """Context manager for spinner progress indicator."""
        if self.rich_formatter:
            # Use rich status spinner
            with self.rich_formatter.status(message) as status:
                yield status
        else:
            # Use traditional spinner
            self.start_spinner(message)
            try:
                yield
            finally:
                self.stop_spinner()
    
    def start_spinner(self, message: str) -> None:
        """Start spinner with message."""
        self._stop_event.clear()
        self._spinner_thread = threading.Thread(
            target=self._spin,
            args=(message,),
            daemon=True
        )
        self._spinner_thread.start()
    
    def stop_spinner(self) -> None:
        """Stop spinner."""
        if self._spinner_thread:
            self._stop_event.set()
            self._spinner_thread.join()
            # Clear the line
            print(f"\r{' ' * 80}\r", end='', flush=True)
    
    def _spin(self, message: str) -> None:
        """Spinner animation loop."""
        i = 0
        while not self._stop_event.is_set():
            char = self._spinner_chars[i % len(self._spinner_chars)]
            print(f"\r{Fore.CYAN}{char} {message}{Style.RESET_ALL}", end='', flush=True)
            time.sleep(0.1)
            i += 1
    
    def progress_bar(self, current: int, total: int, message: str = "", 
                    width: int = 50) -> None:
        """Display progress bar."""
        if total == 0:
            percentage = 100
        else:
            percentage = min(100, (current * 100) // total)
        
        filled = int(width * percentage // 100)
        bar = f"{'█' * filled}{'░' * (width - filled)}"
        
        print(f"\r{Fore.GREEN}{bar}{Style.RESET_ALL} {percentage:3d}% {message}", 
              end='', flush=True)
        
        if current >= total:
            print()  # New line when complete
    
    def step_progress(self, step: int, total_steps: int, step_name: str) -> None:
        """Display step progress."""
        print(f"{Fore.BLUE}[{step}/{total_steps}]{Style.RESET_ALL} {step_name}")


class TableFormatter:
    """Utility for formatting tables with various styles."""
    
    @staticmethod
    def format_table(headers: List[str], rows: List[List[str]], 
                    style: str = "simple") -> str:
        """Format table with specified style."""
        if not rows:
            return "No data to display"
        
        # Calculate column widths
        col_widths = [len(header) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        if style == "simple":
            return TableFormatter._format_simple(headers, rows, col_widths)
        elif style == "grid":
            return TableFormatter._format_grid(headers, rows, col_widths)
        elif style == "markdown":
            return TableFormatter._format_markdown(headers, rows, col_widths)
        else:
            return TableFormatter._format_simple(headers, rows, col_widths)
    
    @staticmethod
    def _format_simple(headers: List[str], rows: List[List[str]], 
                      col_widths: List[int]) -> str:
        """Format table in simple style."""
        lines = []
        
        # Header
        header_row = " | ".join(
            f"{header:<{col_widths[i]}}" 
            for i, header in enumerate(headers)
        )
        lines.append(header_row)
        lines.append("-" * len(header_row))
        
        # Rows
        for row in rows:
            formatted_row = " | ".join(
                f"{str(cell):<{col_widths[i]}}" 
                for i, cell in enumerate(row)
            )
            lines.append(formatted_row)
        
        return "\n".join(lines)
    
    @staticmethod
    def _format_grid(headers: List[str], rows: List[List[str]], 
                    col_widths: List[int]) -> str:
        """Format table in grid style."""
        lines = []
        
        # Top border
        border = "+" + "+".join("-" * (w + 2) for w in col_widths) + "+"
        lines.append(border)
        
        # Header
        header_row = "|" + "|".join(
            f" {header:<{col_widths[i]}} " 
            for i, header in enumerate(headers)
        ) + "|"
        lines.append(header_row)
        lines.append(border)
        
        # Rows
        for row in rows:
            formatted_row = "|" + "|".join(
                f" {str(cell):<{col_widths[i]}} " 
                for i, cell in enumerate(row)
            ) + "|"
            lines.append(formatted_row)
        
        # Bottom border
        lines.append(border)
        
        return "\n".join(lines)
    
    @staticmethod
    def _format_markdown(headers: List[str], rows: List[List[str]], 
                        col_widths: List[int]) -> str:
        """Format table in markdown style."""
        lines = []
        
        # Header
        header_row = "| " + " | ".join(
            f"{header:<{col_widths[i]}}" 
            for i, header in enumerate(headers)
        ) + " |"
        lines.append(header_row)
        
        # Separator
        separator = "| " + " | ".join("-" * w for w in col_widths) + " |"
        lines.append(separator)
        
        # Rows
        for row in rows:
            formatted_row = "| " + " | ".join(
                f"{str(cell):<{col_widths[i]}}" 
                for i, cell in enumerate(row)
            ) + " |"
            lines.append(formatted_row)
        
        return "\n".join(lines)

    # Rich-specific methods (only available when rich formatter is active)

    def print_panel(self, content: str, title: str = None, style: str = None) -> None:
        """Print content in a panel (rich only)."""
        if self.rich_formatter:
            self.rich_formatter.print_panel(content, title, style)
        else:
            if title:
                self.print_subheader(title)
            print(content)

    def print_list(self, items: List[str], bullet_style: str = None) -> None:
        """Print a bulleted list."""
        if self.rich_formatter:
            self.rich_formatter.print_list(items, bullet_style)
        else:
            for item in items:
                print(f"{Fore.BLUE}•{Style.RESET_ALL} {item}")

    def print_step(self, step_num: int, total_steps: int, description: str) -> None:
        """Print step progress indicator."""
        if self.rich_formatter:
            self.rich_formatter.print_step(step_num, total_steps, description)
        else:
            print(f"{Fore.BLUE}[{step_num}/{total_steps}]{Style.RESET_ALL} → {description}")

    def print_separator(self, char: str = "─", style: str = None) -> None:
        """Print a separator line."""
        if self.rich_formatter:
            self.rich_formatter.print_separator(char, style)
        else:
            print(f"{Fore.CYAN}{char * 60}{Style.RESET_ALL}")

    def print_centered(self, text: str, style: str = None) -> None:
        """Print centered text."""
        if self.rich_formatter:
            self.rich_formatter.print_centered(text, style)
        else:
            width = 80  # Default width for fallback
            print(text.center(width))

    def clear(self) -> None:
        """Clear the terminal screen."""
        if self.rich_formatter:
            self.rich_formatter.clear()
        else:
            import os
            os.system('cls' if os.name == 'nt' else 'clear')

    def status_context(self, message: str, spinner: str = "dots"):
        """Context manager for status indicator with spinner (rich only)."""
        if self.rich_formatter:
            return self.rich_formatter.status(message, spinner)
        else:
            # Fallback context manager that just prints the message
            from contextlib import nullcontext
            print(f"{Fore.BLUE}{message}...{Style.RESET_ALL}")
            return nullcontext()

    def has_rich_support(self) -> bool:
        """Check if rich formatting is available."""
        return self.rich_formatter is not None
