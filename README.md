# Agent Framework - Comprehensive Programming Assistant

A powerful, extensible agent framework that provides comprehensive programming assistance through code analysis, generation, optimization, debugging, and documentation capabilities. Now featuring **multi-agent collaboration** and advanced **Model Context Protocol (MCP)** integration for advanced tool access and coordination.

## 🚀 Features

### Core Capabilities
- **Code Analysis**: Complexity analysis, quality metrics, pattern detection, and dependency analysis
- **Code Generation**: Function and class generation, boilerplate creation, test case generation
- **Error Detection**: Syntax checking, runtime error prediction, debugging assistance
- **Code Optimization**: Performance improvements, memory optimization, algorithmic enhancements
- **Documentation**: Docstring generation, API documentation, inline comments

### 🤖 Multi-Agent Collaboration (NEW!)
- **Specialized Agents**: Code analysis, testing, documentation, refactoring, and optimization agents
- **Intelligent Coordination**: Automatic task delegation based on agent capabilities
- **Parallel Execution**: Concurrent task processing across multiple agents
- **Inter-Agent Communication**: Message passing and result sharing between agents
- **Workflow Orchestration**: Complex multi-step workflows with agent coordination
- **Load Balancing**: Intelligent distribution of tasks across available agents

### 🧠 Per-Agent Model Configuration (NEW!)
- **Multi-Model Support**: Different language models for different agents (OpenAI, Anthropic, Azure, local)
- **AutoGen Integration**: Leverage Microsoft AutoGen's tools and workbench patterns
- **Fallback Mechanisms**: Robust error handling with automatic model fallbacks
- **Provider Flexibility**: Support for OpenAI, Anthropic, Azure OpenAI, Ollama, and OpenRouter
- **Cost Optimization**: Use appropriate models for different task complexities
- **Backward Compatibility**: Seamless migration from single-model configurations

### 🔧 Advanced MCP Integration (NEW!)
- **MCP 2025-06-18 Support**: Latest Model Context Protocol specification
- **Server Discovery**: Automatic detection of available MCP servers
- **Tool Access**: Function calling capabilities through MCP servers
- **Resource Management**: File and data access via MCP resources
- **Connection Pooling**: Robust connection management with retry logic
- **Error Handling**: Comprehensive error handling and recovery mechanisms

### User Interface
- **Command-Line Interface**: Comprehensive CLI with subcommands and options
- **Interactive Mode**: Natural language queries and real-time assistance
- **Progress Indicators**: Visual feedback with colored output and progress bars
- **Auto-completion**: Command and parameter completion in interactive mode

### Architecture
- **Plugin System**: Extensible architecture with hot-pluggable capabilities
- **Async Framework**: High-performance asynchronous execution
- **Configuration Management**: Flexible configuration with environment variables and files
- **Comprehensive Logging**: Detailed logging and monitoring capabilities

### 🌟 Advanced AI Coding Capabilities (LATEST!)
- **🧠 Advanced Code Analysis**: Deep code understanding with AST parsing, complexity metrics, and pattern recognition
- **✏️ Intelligent Code Editing**: Safe code modifications with validation and rollback capabilities
- **🐛 Automatic Bug Fixing**: Iterative debugging loops that automatically detect and fix errors
- **📊 Comprehensive Evaluation**: Multi-dimensional code quality assessment with security scanning
- **🔧 Robust Code Generation**: Pattern-aware code generation following established codebase conventions
- **🔄 Automatic Bug Fix Loop**: Systematic error detection with iterative debugging cycles
- **📈 Quality Evaluation Cycles**: Automated static analysis, performance benchmarking, and security scanning
- **🧪 Advanced Testing**: Comprehensive unit test generation and execution with coverage analysis
- **🎯 Intelligent Debugging**: Root cause analysis with confidence scoring and smart suggestions
- **⚡ Performance Optimization**: Automatic detection of performance bottlenecks and optimization suggestions

## 📦 Installation

### Prerequisites
- Python 3.10 or higher
- OpenAI API key (or compatible API)

### Install Dependencies
```bash
# Install using uv (recommended)
uv sync

# Or using pip
pip install -r requirements.txt

# For development
uv sync --dev
# or
pip install -r requirements-dev.txt
```

### Configuration
Set up your API key:
```bash
export AGENT_API_KEY="your-openai-api-key"
export AGENT_MODEL="qwen/qwen3-coder:free"  # Optional: specify model
export AGENT_BASE_URL="https://openrouter.ai/api/v1"  # Optional: API endpoint
```

## 🎯 Quick Start

### Command Line Usage

#### Analyze Code
```bash
# Analyze a Python file
python cli.py analyze --file mycode.py --type all --detailed

# Analyze code complexity
python cli.py analyze --code "def complex_function(): pass" --type complexity

# Check for code smells
python cli.py analyze --file legacy_code.py --type patterns
```

#### Generate Code
```bash
# Generate a function
python cli.py generate function \
  --name calculate_average \
  --description "Calculate average of numbers" \
  --parameters "numbers:List[float]" "exclude_zeros:bool:False" \
  --return-type "float"

# Generate a class
python cli.py generate class \
  --name UserManager \
  --description "Manage user accounts" \
  --attributes "users:List[User]" \
  --methods "add_user:Add a new user" "find_user:Find user by ID"

# Generate boilerplate
python cli.py generate boilerplate \
  --type flask_app \
  --name myapp \
  --features database auth
```

#### Optimize Code
```bash
# Optimize for performance
python cli.py optimize --file slow_code.py --type performance --show-diff

# Memory optimization
python cli.py optimize --code "data = [x for x in range(1000000)]" --type memory

# Algorithm improvements
python cli.py optimize --file algorithms.py --type algorithms --aggressive
```

#### Debug Code
```bash
# Analyze error traceback
python cli.py debug --traceback error.txt --suggest-fixes

# Check for potential errors
python cli.py debug --check-errors --file buggy_code.py

# Syntax checking
python cli.py debug --syntax-check --code "def hello("
```

#### Generate Documentation
```bash
# Generate docstrings
python cli.py document docstrings \
  --file mymodule.py \
  --style google \
  --include-examples

# Generate API documentation
python cli.py document api \
  --file mypackage.py \
  --format markdown \
  --with-toc

# Generate README
python cli.py document readme \
  --project-name "My Project" \
  --description "A cool Python project" \
  --features "Fast" "Reliable" "Easy to use"
```

### 🧠 Per-Agent Model Configuration

#### Configure Different Models for Different Agents
```python
from agent_framework.core.config import ModelConfig, ModelProvider, AgentRoleConfig

# OpenAI for complex code analysis
openai_config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model="gpt-4o",
    api_key="your-openai-key",
    temperature=0.3
)

# Anthropic for documentation
anthropic_config = ModelConfig(
    provider=ModelProvider.ANTHROPIC,
    model="claude-3-5-sonnet-20241022",
    api_key="your-anthropic-key",
    temperature=0.8
)

# Local model for testing
local_config = ModelConfig(
    provider=ModelProvider.OLLAMA,
    model="codellama:7b",
    base_url="http://localhost:11434"
)

# Configure agents with different models
agent_roles = {
    "analyst": AgentRoleConfig(
        name="analyst",
        capabilities=["code_analysis"],
        model_config=openai_config  # Uses GPT-4
    ),
    "writer": AgentRoleConfig(
        name="writer",
        capabilities=["documentation"],
        model_config=anthropic_config  # Uses Claude
    ),
    "tester": AgentRoleConfig(
        name="tester",
        capabilities=["testing"],
        model_config=local_config  # Uses local Ollama
    )
}
```

#### Fallback Configuration
```python
# Primary config with fallbacks
robust_config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model="gpt-4o",
    api_key="your-openai-key",
    fallback_configs=[
        ModelConfig(provider=ModelProvider.ANTHROPIC, model="claude-3-5-sonnet-20241022"),
        ModelConfig(provider=ModelProvider.OPENROUTER, model="qwen/qwen3-coder:free")
    ]
)
```

### 🤖 Multi-Agent Usage

#### Enable Multi-Agent Mode
```python
from agent_framework import AgentOrchestrator
from agent_framework.core.config import FrameworkConfig, MultiAgentConfig, AgentRoleConfig

# Configure multi-agent system
config = FrameworkConfig(
    multi_agent=MultiAgentConfig(
        enabled=True,
        max_agents=5,
        coordination_strategy="capability_based",
        agent_roles={
            "code_analyst": AgentRoleConfig(
                name="code_analyst",
                capabilities=["code_analysis", "error_detection"],
                system_message="You are a code analysis expert..."
            ),
            "tester": AgentRoleConfig(
                name="tester",
                capabilities=["testing"],
                system_message="You are a testing expert..."
            )
        }
    )
)

orchestrator = AgentOrchestrator(config)
await orchestrator.initialize()
```

#### Execute Multi-Agent Tasks
```python
from agent_framework.core.types import Task

# Complex task requiring multiple agents
task = Task(
    name="comprehensive_code_review",
    description="Analyze code quality, generate tests, and create documentation",
    task_type="workflow",
    parameters={
        "file_path": "src/main.py",
        "include_tests": True,
        "include_docs": True
    }
)

# Execute with automatic agent coordination
result = await orchestrator.execute_multi_agent_task(task)
```

#### Direct Agent Delegation
```python
# Delegate to specific agent type
result = await orchestrator.delegate_task_to_agent(task, "code_analyst")

# Monitor multi-agent system
status = await orchestrator.get_multi_agent_status()
print(f"Active agents: {status['registry_stats']['total_agents']}")
```

### Interactive Mode

Start interactive mode for natural language assistance:

```bash
python cli.py interactive
```

In interactive mode, you can:

```
agent> How can I optimize this function for better performance?
agent> analyze complexity def my_function(): pass
agent> generate function --name hello --description "Say hello"
agent> What's wrong with my code?
agent> help
agent> status
```

### 🌟 Advanced AI Coding Capabilities Usage

#### Advanced Code Implementation
```python
from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)

# Configure advanced capabilities
capabilities = AgentCapabilities(
    enable_automatic_bug_fixing=True,
    enable_automatic_evaluation=True,
    enable_advanced_code_generation=True,
    enable_comprehensive_testing=True
)

# Initialize advanced orchestrator
orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

# Generate robust code with comprehensive validation
requirements = {
    "type": "function",
    "name": "process_data",
    "description": "Process user data with validation",
    "async": True,
    "error_handling": True
}

result = await orchestrator.advanced_code_implementation(
    requirements=requirements,
    file_path="src/processor.py"
)

if result["success"]:
    print(f"Generated code: {result['generated_code']}")
    print(f"Quality score: {result['evaluation'].overall_score}")
```

#### Automatic Bug Fixing
```python
# Automatically fix bugs with iterative debugging
try:
    # Some problematic code
    result = problematic_function()
except Exception as e:
    fix_result = await orchestrator.automatic_bug_fixing(
        error=e,
        code_content=get_function_code(),
        file_path="src/module.py",
        test_files=["tests/test_module.py"]
    )

    if fix_result["success"]:
        print(f"Bug fixed in {len(fix_result['session'].fix_attempts)} attempts!")
        print(f"Quality improved to: {fix_result['evaluation'].overall_quality.value}")
```

#### Comprehensive Code Analysis
```python
# Deep code analysis with quality assessment
analysis = await orchestrator.comprehensive_code_analysis(
    code_content=code_to_analyze,
    file_path="src/module.py",
    analysis_depth="comprehensive"
)

print(f"Quality Score: {analysis['quality_score']:.2f}")
print(f"Complexity: {analysis['context'].complexity_metrics}")
print("Improvement Suggestions:")
for suggestion in analysis["suggestions"]:
    print(f"  • {suggestion}")
```

#### Full Enhancement Cycle
```python
# Run complete enhancement cycle
results = await orchestrator.run_full_enhancement_cycle(
    code_content=legacy_code,
    file_path="src/legacy.py",
    requirements={
        "type": "enhancement",
        "goals": ["improve_performance", "add_error_handling", "modernize_syntax"]
    }
)

print(f"Enhancement successful: {results['overall_success']}")
print(f"Final quality: {results['evaluation'].overall_quality.value}")
```

## 🏗️ Architecture

### Plugin System

The framework uses a plugin-based architecture with the following core plugins:

- **Code Analysis Plugin**: Complexity analysis, quality metrics, pattern detection
- **Code Generation Plugin**: Template-based code generation and boilerplate creation
- **Error Detection Plugin**: Syntax checking, error prediction, debugging assistance
- **Code Optimization Plugin**: Performance and memory optimization suggestions
- **Documentation Plugin**: Docstring and documentation generation

### Core Components

```
agent-framework/
├── src/
│   └── agent_framework/  # Main package
│       ├── core/         # Core framework components
│       │   ├── orchestrator.py   # Main orchestration system
│       │   ├── config.py         # Configuration management
│       │   └── types.py          # Core data types
│       ├── cli/          # Command-line interface
│       │   ├── core.py           # Main CLI application
│       │   ├── commands/         # CLI command implementations
│       │   ├── interactive.py    # Interactive mode
│       │   └── utils.py          # CLI utilities
│       ├── plugins/      # Plugin system
│       │   ├── manager.py        # Plugin management
│       │   ├── loader.py         # Plugin loading
│       │   └── registry.py       # Plugin registry
│       ├── context/      # Context management
│       ├── execution/    # Task execution
│       └── communication/ # Message broker
├── tests/                # Test suite
├── docs/                 # Documentation
├── examples/             # Usage examples
├── scripts/              # Utility scripts
└── .github/              # GitHub workflows
```

## 🔧 Configuration

### Configuration File

Create a `config.yaml` file:

```yaml
name: "My Programming Assistant"
debug: false

model:
  model: "qwen/qwen3-coder:free"
  api_key: "${AGENT_API_KEY}"
  base_url: "https://openrouter.ai/api/v1"
  max_tokens: 4096
  temperature: 0.7

plugins:
  auto_load_plugins: true
  plugin_directories: ["plugins"]

logging:
  level: "INFO"
  file_path: "agent.log"

cache:
  enabled: true
  cache_type: "memory"
  ttl_seconds: 3600
```

### Environment Variables

```bash
# Required
export AGENT_API_KEY="your-api-key"

# Optional
export AGENT_MODEL="qwen/qwen3-coder:free"
export AGENT_BASE_URL="https://openrouter.ai/api/v1"
export AGENT_DEBUG="false"
export AGENT_CACHE_TYPE="memory"
```

## 📚 Examples

### Programmatic Usage

```python
import asyncio
from agent_framework.core.config import FrameworkConfig
from agent_framework.core.orchestrator import AgentOrchestrator

async def main():
    # Initialize framework
    config = FrameworkConfig.from_env()
    orchestrator = AgentOrchestrator(config)
    await orchestrator.initialize()

    # Analyze code
    code = "def hello(): print('Hello, World!')"
    result = await orchestrator.run_agent_task(
        f"Analyze this code for complexity: {code}"
    )
    print(result)

    # Generate code
    result = await orchestrator.run_agent_task(
        "Generate a Python function that calculates factorial"
    )
    print(result)

    # Cleanup
    await orchestrator.shutdown()

asyncio.run(main())
```

### Custom Plugin Development

```python
from agent_framework.core.types import PluginInterface, PluginCapability

class MyCustomPlugin(PluginInterface):
    @property
    def name(self) -> str:
        return "my_custom_plugin"

    @property
    def version(self) -> str:
        return "1.0.0"

    async def initialize(self, config):
        # Initialize plugin
        pass

    async def execute(self, request):
        # Handle plugin requests
        pass

    async def get_capabilities(self):
        return [
            PluginCapability(
                name="my_capability",
                description="My custom capability",
                input_schema={...},
                output_schema={...}
            )
        ]
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=agent_framework

# Run specific test categories
pytest tests/test_plugins.py
pytest tests/test_cli.py
```

## 📖 Documentation

### CLI Help

```bash
# General help
python cli.py --help

# Command-specific help
python cli.py analyze --help
python cli.py generate --help
python cli.py optimize --help
```

### API Documentation

Generate API documentation:

```bash
python cli.py document api --file agent_framework/ --format markdown > API.md
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run the test suite: `pytest`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Development Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework

# Install development dependencies
make install-dev
# or manually:
uv sync --dev

# Install pre-commit hooks
pre-commit install

# Run tests
make test
# or manually:
pytest

# Run all quality checks
make check

# Format code
make format
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on top of the excellent [AutoGen](https://github.com/microsoft/autogen) framework
- Uses [OpenAI](https://openai.com/) and compatible APIs for AI capabilities
- Inspired by modern development tools and practices

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [Full documentation](https://docs.example.com)
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/agent-framework/issues)

---

**Happy Coding! 🚀**