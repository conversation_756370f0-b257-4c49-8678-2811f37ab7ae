# Frequently Asked Questions (FAQ)

Common questions and answers about the Agent Framework.

## General Questions

### What is the Agent Framework?

The Agent Framework is a comprehensive programming assistant that uses AI to help with code analysis, generation, optimization, debugging, and documentation. It provides both command-line and programmatic interfaces for integrating AI-powered code assistance into your development workflow.

### What programming languages are supported?

Currently, the framework primarily supports **Python** with comprehensive analysis and generation capabilities. Support for other languages (JavaScript, TypeScript, Java, C++) is planned for future releases.

### Do I need an internet connection to use the framework?

For most features, yes, since the framework relies on AI models hosted by providers like OpenAI, Anthropic, or OpenRouter. However, you can use local models with Ollama for offline operation with reduced capabilities.

### Is my code sent to external services?

Yes, when using cloud-based AI providers (OpenAI, Anthropic, etc.), your code is sent to their APIs for processing. For privacy-sensitive code, consider using local models with Ollama or ensure your AI provider's terms meet your security requirements.

## Installation and Setup

### What Python version is required?

Python 3.10 or higher is required. The framework uses modern Python features and type hints that require recent Python versions.

### Can I use the framework without an API key?

No, you need an API key from at least one supported provider:
- OpenAI (recommended for best performance)
- Anthropic (good for documentation tasks)
- OpenRouter (cost-effective access to multiple models)
- Local models via Ollama (for offline use)

### How do I get an API key?

1. **OpenAI**: Visit [platform.openai.com](https://platform.openai.com) and create an account
2. **Anthropic**: Visit [console.anthropic.com](https://console.anthropic.com) and sign up
3. **OpenRouter**: Visit [openrouter.ai](https://openrouter.ai) for access to multiple models

### Why am I getting "command not found" errors?

This usually means:
1. The framework isn't installed: `pip install -e .`
2. You're not in a virtual environment: `source .venv/bin/activate`
3. Use the Python module directly: `python -m agent_framework.cli.core`

## Usage Questions

### How do I analyze my entire project?

```bash
# Analyze all Python files in src directory
agent-framework analyze --file src/ --type all --detailed

# Or analyze specific patterns
find src/ -name "*.py" -exec agent-framework analyze --file {} --type quality \;
```

### Can I customize the analysis thresholds?

Yes, you can set custom thresholds:

```bash
# Command line
agent-framework analyze --file code.py --type complexity --threshold 15

# Configuration file
```yaml
analysis:
  thresholds:
    cyclomatic_complexity: 15
    maintainability_index: 60
```

### How do I generate code for specific frameworks?

```bash
# Flask application
agent-framework generate boilerplate --type flask_app --name myapp --features database auth

# FastAPI application  
agent-framework generate boilerplate --type fastapi_app --name myapi --features database cors

# Test cases
agent-framework generate tests --file mymodule.py --framework pytest
```

### Can I use different AI models for different tasks?

Yes, with per-agent model configuration:

```yaml
multi_agent:
  agent_roles:
    code_analyst:
      model_config_ref: "openai_config"  # Use GPT-4 for analysis
    writer:
      model_config_ref: "anthropic_config"  # Use Claude for documentation
```

## Performance Questions

### Why is the framework slow?

Common causes and solutions:

1. **Large files**: Use `--type complexity` instead of `--type all` for quick analysis
2. **Network latency**: Check your internet connection
3. **Model choice**: Faster models like `gpt-3.5-turbo` are quicker than `gpt-4`
4. **No caching**: Enable caching in your configuration

### How can I speed up analysis?

1. **Enable caching**:
   ```yaml
   cache:
     enabled: true
     cache_type: "memory"
   ```

2. **Use faster models**:
   ```bash
   export AGENT_MODEL="gpt-3.5-turbo"
   ```

3. **Analyze incrementally**:
   ```bash
   # Only analyze changed files
   git diff --name-only | grep '\.py$' | xargs -I {} agent-framework analyze --file {}
   ```

### Does the framework cache results?

Yes, when enabled in configuration. The framework caches:
- Analysis results for unchanged files
- Model responses for identical requests
- Context information for faster subsequent operations

## Error Questions

### I'm getting "Invalid API key" errors

1. **Check your API key format**:
   ```bash
   echo $AGENT_API_KEY  # Should start with sk- for most providers
   ```

2. **Verify the key is active**: Test with a simple API call
3. **Check provider-specific requirements**: Some providers need additional headers

### Why do I get "Rate limit exceeded" errors?

AI providers limit requests per minute/hour. Solutions:

1. **Wait and retry**: Rate limits reset over time
2. **Use different models**: Some models have higher limits
3. **Switch providers**: Try OpenRouter for higher limits
4. **Implement delays**: Add delays between requests in batch operations

### The framework says my code has syntax errors, but it's valid

1. **Check file encoding**: Ensure files are UTF-8 encoded
2. **Escape shell characters**: Use quotes around code strings
3. **Use file input**: Avoid shell escaping issues by using `--file` instead of `--code`

## Feature Questions

### Can I extend the framework with custom plugins?

Yes, the framework has a plugin system. See the [Plugin Development Guide](../technical/plugin-development.md) for details.

### Does the framework support team collaboration?

The framework itself is single-user, but you can:
- Share configuration files across teams
- Integrate into CI/CD pipelines
- Use shared coding standards and thresholds
- Generate reports for team review

### Can I integrate the framework into my IDE?

Currently, the framework provides CLI and Python API interfaces. IDE integration is planned for future releases. You can create custom IDE extensions using the Python API.

### Does the framework work with version control?

Yes, you can:
- Set up pre-commit hooks for quality checks
- Analyze only changed files in CI/CD
- Track quality metrics over time
- Generate reports for code reviews

## Configuration Questions

### Where should I put my configuration file?

The framework looks for configuration in this order:
1. File specified with `--config` option
2. `config.yaml` in current directory
3. `~/.agent-framework/config.yaml`
4. Environment variables

### Can I have different configurations for different projects?

Yes, use project-specific configuration files:

```bash
# Project A
cd project-a
agent-framework --config project-a-config.yaml analyze --file src/

# Project B  
cd project-b
agent-framework --config project-b-config.yaml analyze --file src/
```

### How do I configure the framework for my team?

1. **Create a shared configuration file**:
   ```yaml
   # team-config.yaml
   analysis:
     thresholds:
       cyclomatic_complexity: 10
       maintainability_index: 70
   ```

2. **Document team standards**: Include configuration in your project repository
3. **Use environment variables**: For sensitive data like API keys
4. **Set up CI/CD**: Enforce standards automatically

## Troubleshooting Questions

### How do I enable debug mode?

```bash
# Environment variable
export AGENT_DEBUG=true

# Command line
agent-framework --verbose analyze --file code.py

# Configuration file
debug: true
```

### Where are the log files?

Default locations:
- Current directory: `agent.log`
- Configured location: Check your `config.yaml`
- System logs: `/var/log/agent-framework/` (if configured)

### How do I report a bug?

1. **Check existing issues**: [GitHub Issues](https://github.com/yourusername/agent-framework/issues)
2. **Gather information**:
   - Framework version: `agent-framework --version`
   - Python version: `python --version`
   - Error messages and logs
   - Minimal reproduction steps
3. **Create a new issue** with all relevant details

## Advanced Questions

### Can I run the framework in production?

The framework is designed for development assistance. For production use:
- Consider rate limits and costs
- Implement proper error handling
- Use caching to reduce API calls
- Monitor usage and performance

### How do I contribute to the framework?

1. **Read the contributing guide**: [Contributing](../development/contributing.md)
2. **Set up development environment**: Follow development installation
3. **Find issues to work on**: Check GitHub issues labeled "good first issue"
4. **Submit pull requests**: Follow the contribution workflow

### Can I use the framework commercially?

Yes, the framework is open source under the MIT license. However, check the terms of service for your chosen AI provider regarding commercial use of their APIs.

## Getting More Help

### Where can I get additional support?

1. **Documentation**: Browse the complete [documentation](../README.md)
2. **Community Discord**: Join our [Discord server](https://discord.gg/example)
3. **GitHub Discussions**: Participate in [GitHub Discussions](https://github.com/yourusername/agent-framework/discussions)
4. **Stack Overflow**: Tag questions with `agent-framework`

### How do I stay updated?

1. **Watch the repository**: Get notifications for releases
2. **Follow releases**: Check [GitHub Releases](https://github.com/yourusername/agent-framework/releases)
3. **Join the community**: Discord and GitHub Discussions
4. **Read the changelog**: Review changes in each release

### Can I request new features?

Yes! Feature requests are welcome:
1. **Check existing requests**: Search GitHub issues
2. **Create a feature request**: Use the feature request template
3. **Participate in discussions**: Help refine and prioritize features
4. **Consider contributing**: Implement features yourself

## See Also

- [Common Issues](common-issues.md) - Troubleshooting guide
- [Installation Guide](../getting-started/installation.md) - Setup instructions
- [Configuration Guide](../user-guides/configuration.md) - Configuration options
- [CLI Reference](../user-guides/cli-reference.md) - Command documentation
