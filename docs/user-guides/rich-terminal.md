# Rich Terminal Interface

The Agent Framework now features a comprehensive rich terminal interface that provides an enhanced command-line experience with beautiful formatting, interactive elements, and advanced progress tracking.

## 🌟 Features

### Rich Terminal Formatting
- **Color-coded output** for different message types (success, error, warning, info, debug)
- **Formatted tables** with customizable styling and borders
- **Syntax highlighting** for code blocks in multiple languages
- **Panels and layouts** for organized content presentation
- **Interactive prompts** with validation and auto-completion

### Advanced Progress Tracking
- **Real-time progress bars** with percentage and time estimates
- **Multi-step workflows** with dependency management
- **Status indicators** with animated spinners
- **Workflow visualization** with phase separation and completion tracking

### Interactive User Experience
- **Actionable error reporting** with suggested solutions
- **Loading spinners** for long-running operations
- **Interactive menus** with keyboard navigation
- **Form builders** for collecting user input
- **Confirmation dialogs** with detailed information

## 🚀 Quick Start

### Basic Usage

```python
from agent_framework.cli.rich_formatter import RichTerminalFormatter

# Initialize the formatter
formatter = RichTerminalFormatter()

# Display different message types
formatter.print_success("Operation completed successfully!")
formatter.print_error("An error occurred")
formatter.print_warning("This is a warning")
formatter.print_info("Here's some information")

# Create formatted tables
headers = ["Name", "Status", "Progress"]
rows = [
    ["Task 1", "✅ Complete", "100%"],
    ["Task 2", "🔄 Running", "75%"],
    ["Task 3", "⏳ Pending", "0%"]
]
formatter.print_table(headers, rows, "Task Status")

# Display code with syntax highlighting
code = '''
def hello_world():
    print("Hello, Rich Terminal!")
    return True
'''
formatter.print_code(code, "python", "Example Code")
```

### Progress Tracking

```python
from agent_framework.cli.progress_tracker import AdvancedProgressTracker

# Create progress tracker
tracker = AdvancedProgressTracker()

# Define operations
tracker.create_operation("download", "Downloading", "Downloading files", 100)
tracker.create_operation("process", "Processing", "Processing data", 50)

# Execute with progress display
with tracker.progress_context("download", "process", title="File Processing"):
    # Start and update operations
    tracker.start_operation("download")
    for i in range(0, 101, 10):
        tracker.update_operation("download", completed_work=i)
        time.sleep(0.1)
    tracker.complete_operation("download")
    
    # Process second operation
    tracker.start_operation("process")
    # ... similar updates
    tracker.complete_operation("process")

# Print summary
tracker.print_summary()
```

### Workflow Visualization

```python
from agent_framework.cli.workflow_visualizer import WorkflowVisualizer, WorkflowPhase

# Create workflow visualizer
visualizer = WorkflowVisualizer()

# Define workflow steps with dependencies
visualizer.add_step("init", "Initialize", "Setup system", WorkflowPhase.INITIALIZATION)
visualizer.add_step("process", "Process", "Process data", WorkflowPhase.EXECUTION, ["init"])
visualizer.add_step("validate", "Validate", "Validate results", WorkflowPhase.VALIDATION, ["process"])

# Start workflow
visualizer.start_workflow("Data Processing Pipeline")

# Execute steps
visualizer.start_step("init")
visualizer.complete_step("init")

visualizer.start_step("process")
visualizer.complete_step("process")

visualizer.start_step("validate")
visualizer.complete_step("validate")

# Print summary
visualizer.print_workflow_summary()
```

### Interactive UX

```python
from agent_framework.cli.interactive_ux import InteractiveUX, ActionableError

# Create interactive UX
ux = InteractiveUX()

# Show welcome banner
ux.show_welcome_banner("My Application", version="1.0.0", description="Enhanced CLI app")

# Display actionable error
error = ActionableError(
    message="Connection failed",
    suggestions=["Check network", "Verify credentials", "Try again"],
    error_code="CONN_001"
)
ux.show_actionable_error(error)

# Create interactive menu
options = ["Start processing", "View logs", "Exit"]
choice = ux.create_interactive_menu("What would you like to do?", options)

# Show loading with spinner
def long_task():
    time.sleep(3)
    return "Task completed"

result = ux.show_loading_spinner("Processing...", long_task)
```

## 📦 Components

### RichTerminalFormatter
The core formatting component that provides:
- Message formatting with icons and colors
- Table creation and styling
- Code syntax highlighting
- Panel and layout management
- Interactive prompts

### AdvancedProgressTracker
Comprehensive progress tracking with:
- Multiple concurrent operations
- Real-time progress updates
- Time estimation and elapsed time
- Operation status management
- Summary reporting

### WorkflowVisualizer
Workflow management and visualization:
- Step dependency tracking
- Phase-based organization
- Visual progress indicators
- Completion status tracking
- Tree-view workflow display

### InteractiveUX
Enhanced user experience features:
- Welcome banners and headers
- Actionable error reporting
- Loading indicators
- Multi-step progress displays
- Interactive forms and menus

### Enhanced CLIUtils
Upgraded CLI utilities with:
- Rich formatting integration
- Backward compatibility
- Fallback support for environments without rich
- Extended utility methods

## 🎨 Customization

### Color Schemes
```python
# Customize colors
formatter = RichTerminalFormatter()
formatter.colors['success'] = 'bright_green'
formatter.colors['error'] = 'bright_red'
formatter.colors['warning'] = 'orange3'
```

### Icons and Symbols
```python
# Customize icons
formatter.icons['success'] = '🎉'
formatter.icons['error'] = '💥'
formatter.icons['warning'] = '⚡'
```

### Table Styling
```python
# Create custom table
table = formatter.create_table("Custom Table", show_lines=True)
table.add_column("Column 1", style="cyan")
table.add_column("Column 2", style="magenta")
```

## 🔧 Configuration

### Environment Variables
- `RICH_FORCE_TERMINAL`: Force rich output even when not in a terminal
- `NO_COLOR`: Disable all color output
- `TERM`: Terminal type detection

### Programmatic Configuration
```python
# Initialize with custom settings
formatter = RichTerminalFormatter(
    force_terminal=True,  # Force terminal mode
    width=120            # Set custom width
)

# Initialize CLI utils with rich disabled
utils = CLIUtils(use_rich=False)  # Use fallback mode
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
python3 -m pytest tests/test_rich_terminal.py -v
```

Run the standalone demo:
```bash
python3 test_rich_standalone.py
```

## 📋 Examples

See the `examples/` directory for comprehensive demonstrations:
- `rich_terminal_demo.py`: Full feature demonstration
- `test_rich_standalone.py`: Standalone testing script

## 🔄 Backward Compatibility

The enhanced terminal interface maintains full backward compatibility:
- Existing code continues to work unchanged
- Automatic fallback to basic formatting when rich is unavailable
- Optional rich features can be disabled
- Progressive enhancement approach

## 🐛 Troubleshooting

### Rich Library Not Available
If you see "Rich library not available" errors:
```bash
pip install rich>=13.0.0
```

### Terminal Compatibility Issues
For terminals that don't support rich formatting:
```python
# Force fallback mode
utils = CLIUtils(use_rich=False)
```

### Performance Considerations
Rich formatting may be slower in some environments:
- Use `force_terminal=False` for non-interactive scripts
- Consider disabling animations for batch processing
- Use basic formatting for high-frequency output

## 🤝 Contributing

When contributing to the rich terminal interface:
1. Maintain backward compatibility
2. Add fallback support for new features
3. Include comprehensive tests
4. Update documentation and examples
5. Follow the existing code style

## 📚 API Reference

For detailed API documentation, see the docstrings in each module:
- `agent_framework.cli.rich_formatter`
- `agent_framework.cli.progress_tracker`
- `agent_framework.cli.workflow_visualizer`
- `agent_framework.cli.interactive_ux`
- `agent_framework.cli.utils`

## 🎯 Future Enhancements

Planned improvements:
- Theme system for consistent styling
- Plugin architecture for custom formatters
- Integration with logging systems
- Advanced chart and graph support
- Terminal recording and playback
