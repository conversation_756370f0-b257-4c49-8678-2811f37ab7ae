# Configuration Guide

This guide covers all configuration options for the Agent Framework, including environment variables, configuration files, and advanced settings.

## Overview

The Agent Framework can be configured through:

1. **Environment Variables** (recommended for API keys)
2. **Configuration Files** (YAML format)
3. **Command Line Arguments** (for specific operations)
4. **Programmatic Configuration** (for API usage)

## Environment Variables

### Required Variables

```bash
# API Key (choose one provider)
export AGENT_API_KEY="your-api-key"

# Model selection
export AGENT_MODEL="gpt-4o"  # OpenAI
# export AGENT_MODEL="claude-3-5-sonnet-20241022"  # Anthropic
# export AGENT_MODEL="qwen/qwen3-coder:free"  # OpenRouter

# Base URL (optional, provider-specific)
export AGENT_BASE_URL="https://api.openai.com/v1"  # OpenAI (default)
# export AGENT_BASE_URL="https://api.anthropic.com"  # Anthropic
# export AGENT_BASE_URL="https://openrouter.ai/api/v1"  # OpenRouter
```

### Optional Variables

```bash
# Framework behavior
export AGENT_DEBUG="false"              # Enable debug mode
export AGENT_CACHE_TYPE="memory"        # Cache type: memory, disk, redis
export AGENT_LOG_LEVEL="INFO"           # Logging level
export AGENT_WORKSPACE="/path/to/project"  # Default workspace

# Performance tuning
export AGENT_MAX_TOKENS="4096"          # Maximum tokens per request
export AGENT_TEMPERATURE="0.7"          # Model temperature
export AGENT_TIMEOUT="60"               # Request timeout in seconds
export AGENT_MAX_RETRIES="3"            # Maximum retry attempts

# Multi-agent settings
export AGENT_MAX_AGENTS="5"             # Maximum concurrent agents
export AGENT_COORDINATION_STRATEGY="capability_based"  # Coordination strategy
```

## Configuration Files

### Basic Configuration

Create a `config.yaml` file in your project directory:

```yaml
# Basic configuration
name: "My Programming Assistant"
debug: false

# Model configuration
model:
  model: "gpt-4o"
  api_key: "${AGENT_API_KEY}"
  base_url: "https://api.openai.com/v1"
  max_tokens: 4096
  temperature: 0.7
  timeout_seconds: 60

# Plugin settings
plugins:
  auto_load_plugins: true
  plugin_directories: ["plugins", "custom_plugins"]
  allowed_imports: ["os", "sys", "json", "yaml"]

# Logging configuration
logging:
  level: "INFO"
  file_path: "logs/agent.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_file_size: "10MB"
  backup_count: 5

# Cache settings
cache:
  enabled: true
  cache_type: "memory"  # memory, disk, redis
  ttl_seconds: 3600
  max_size: "100MB"
```

### Advanced Configuration

```yaml
# Advanced multi-agent configuration
multi_agent:
  enabled: true
  max_agents: 5
  coordination_strategy: "capability_based"  # capability_based, round_robin, priority, load_balanced
  
  # Agent role definitions
  agent_roles:
    code_analyst:
      name: "code_analyst"
      description: "Code analysis specialist"
      capabilities: ["code_analysis", "error_detection"]
      system_message: "You are a code analysis expert..."
      priority: 1
      max_concurrent_tasks: 3
      
    tester:
      name: "tester"
      description: "Testing specialist"
      capabilities: ["testing", "test_generation"]
      system_message: "You are a testing expert..."
      priority: 2
      max_concurrent_tasks: 2

# MCP (Model Context Protocol) configuration
mcp:
  enabled: true
  servers:
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/project"]
      description: "File system access"
      timeout_seconds: 30
      
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
      description: "Web content fetching"
      timeout_seconds: 60

# Advanced capabilities
advanced_capabilities:
  enabled: true
  enable_automatic_bug_fixing: true
  enable_automatic_evaluation: true
  enable_advanced_code_generation: true
  enable_comprehensive_testing: true
  max_fix_iterations: 5
  evaluation_on_every_change: true
  rollback_on_critical_issues: true

# Performance settings
performance:
  async_execution: true
  max_concurrent_requests: 10
  request_timeout: 120
  retry_attempts: 3
  retry_delay: 1.0
  
# Security settings
security:
  validate_code_execution: true
  sandbox_plugins: true
  allowed_file_extensions: [".py", ".js", ".ts", ".java", ".cpp"]
  max_file_size: "10MB"
```

## Per-Agent Model Configuration

Configure different models for different agents:

```yaml
# Multi-model configuration
model_configs:
  openai_config:
    provider: "openai"
    model: "gpt-4o"
    api_key: "${OPENAI_API_KEY}"
    temperature: 0.3
    max_tokens: 4096
    
  anthropic_config:
    provider: "anthropic"
    model: "claude-3-5-sonnet-20241022"
    api_key: "${ANTHROPIC_API_KEY}"
    temperature: 0.8
    max_tokens: 4096
    
  local_config:
    provider: "ollama"
    model: "codellama:7b"
    base_url: "http://localhost:11434"
    temperature: 0.5

# Assign models to agent roles
multi_agent:
  agent_roles:
    code_analyst:
      name: "code_analyst"
      capabilities: ["code_analysis"]
      model_config_ref: "openai_config"  # Use GPT-4 for analysis
      
    writer:
      name: "writer"
      capabilities: ["documentation"]
      model_config_ref: "anthropic_config"  # Use Claude for writing
      
    tester:
      name: "tester"
      capabilities: ["testing"]
      model_config_ref: "local_config"  # Use local model for testing
```

## Provider-Specific Configuration

### OpenAI Configuration

```yaml
model:
  provider: "openai"
  model: "gpt-4o"  # or gpt-4, gpt-3.5-turbo
  api_key: "${OPENAI_API_KEY}"
  base_url: "https://api.openai.com/v1"
  organization: "your-org-id"  # optional
  temperature: 0.7
  max_tokens: 4096
  top_p: 1.0
  frequency_penalty: 0.0
  presence_penalty: 0.0
```

### Anthropic Configuration

```yaml
model:
  provider: "anthropic"
  model: "claude-3-5-sonnet-20241022"
  api_key: "${ANTHROPIC_API_KEY}"
  base_url: "https://api.anthropic.com"
  temperature: 0.8
  max_tokens: 4096
  top_p: 1.0
```

### Azure OpenAI Configuration

```yaml
model:
  provider: "azure"
  model: "gpt-4"
  api_key: "${AZURE_OPENAI_API_KEY}"
  base_url: "https://your-resource.openai.azure.com"
  api_version: "2024-02-15-preview"
  deployment_name: "gpt-4-deployment"
  temperature: 0.7
  max_tokens: 4096
```

### OpenRouter Configuration

```yaml
model:
  provider: "openrouter"
  model: "qwen/qwen3-coder:free"  # or other available models
  api_key: "${OPENROUTER_API_KEY}"
  base_url: "https://openrouter.ai/api/v1"
  temperature: 0.7
  max_tokens: 4096
  extra_headers:
    "HTTP-Referer": "https://your-site.com"
    "X-Title": "Agent Framework"
```

### Local Models (Ollama)

```yaml
model:
  provider: "ollama"
  model: "codellama:7b"  # or llama2, mistral, etc.
  base_url: "http://localhost:11434"
  temperature: 0.5
  max_tokens: 2048
  
# Ensure Ollama is running:
# ollama serve
# ollama pull codellama:7b
```

## Configuration Validation

### Validate Configuration

```bash
# Test configuration file
agent-framework --config config.yaml --help

# Validate specific settings
agent-framework --config config.yaml analyze --code "def test(): pass" --type complexity
```

### Configuration Schema

The framework validates configuration against a schema. Common validation errors:

```yaml
# ❌ Invalid - missing required fields
model:
  model: "gpt-4o"
  # Missing api_key

# ❌ Invalid - wrong type
multi_agent:
  max_agents: "five"  # Should be integer

# ✅ Valid - complete configuration
model:
  model: "gpt-4o"
  api_key: "${AGENT_API_KEY}"
  temperature: 0.7
```

## Configuration Precedence

Configuration is loaded in this order (later values override earlier ones):

1. **Default values** (built into the framework)
2. **Configuration file** (`config.yaml`)
3. **Environment variables** (`AGENT_*`)
4. **Command line arguments** (`--config`, `--verbose`, etc.)

Example:
```bash
# config.yaml has temperature: 0.7
# Environment has AGENT_TEMPERATURE=0.5
# Command line has --temperature 0.3
# Final value: 0.3 (command line wins)
```

## Dynamic Configuration

### Runtime Configuration Changes

```python
# Programmatic configuration updates
from agent_framework.core.config import FrameworkConfig

config = FrameworkConfig.from_file("config.yaml")
config.model.temperature = 0.5  # Update temperature
config.logging.level = "DEBUG"  # Enable debug logging

orchestrator = AgentOrchestrator(config)
```

### Configuration Reloading

```bash
# Some settings can be reloaded without restart
# Send SIGHUP to reload configuration (if supported)
kill -HUP $(pgrep -f agent-framework)
```

## Best Practices

### Security

1. **Never commit API keys** to version control
2. **Use environment variables** for sensitive data
3. **Rotate API keys** regularly
4. **Limit file access** with appropriate permissions

```bash
# Good: Use environment variables
export AGENT_API_KEY="sk-..."

# Bad: Hardcode in config file
# api_key: "sk-actual-key-here"  # DON'T DO THIS
```

### Performance

1. **Enable caching** for better performance
2. **Tune model parameters** for your use case
3. **Use appropriate models** for different tasks
4. **Monitor resource usage**

```yaml
# Performance-optimized configuration
cache:
  enabled: true
  cache_type: "memory"
  ttl_seconds: 7200

performance:
  max_concurrent_requests: 5
  request_timeout: 60
```

### Maintainability

1. **Use configuration files** for complex setups
2. **Document custom settings** with comments
3. **Version control** configuration templates
4. **Test configuration changes** before deployment

## Troubleshooting Configuration

### Common Issues

1. **Invalid YAML syntax**:
   ```bash
   # Validate YAML syntax
   python -c "import yaml; yaml.safe_load(open('config.yaml'))"
   ```

2. **Environment variable not expanded**:
   ```bash
   # Check variable expansion
   echo $AGENT_API_KEY
   ```

3. **Configuration not found**:
   ```bash
   # Specify explicit path
   agent-framework --config /full/path/to/config.yaml
   ```

### Debug Configuration

```bash
# Show effective configuration
export AGENT_DEBUG=true
agent-framework --verbose --config config.yaml analyze --code "def test(): pass"
```

## See Also

- [Installation Guide](../getting-started/installation.md)
- [Multi-Agent Setup](multi-agent-setup.md)
- [Model Configuration](model-configuration.md)
- [Common Issues](../troubleshooting/common-issues.md)
